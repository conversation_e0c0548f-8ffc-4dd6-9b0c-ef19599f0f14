define("ace/ext/code_lens",["require","exports","module","ace/lib/event","ace/lib/lang","ace/lib/dom","ace/editor","ace/config"],function(e,t,n){"use strict";function o(e){var t=e.$textLayer,n=t.$lenses;n&&n.forEach(function(e){e.remove()}),t.$lenses=null}function u(e,t){var n=e&t.CHANGE_LINES||e&t.CHANGE_FULL||e&t.CHANGE_SCROLL||e&t.CHANGE_TEXT;if(!n)return;var r=t.session,i=t.session.lineWidgets,u=t.$textLayer,a=u.$lenses;if(!i){a&&o(t);return}var f=t.$textLayer.$lines.cells,l=t.layerConfig,c=t.$padding;a||(a=u.$lenses=[]);var h=0;for(var p=0;p<f.length;p++){var d=f[p].row,v=i[d],m=v&&v.lenses;if(!m||!m.length)continue;var g=a[h];g||(g=a[h]=s.buildDom(["div",{"class":"ace_codeLens"}],t.container)),g.style.height=l.lineHeight+"px",h++;for(var y=0;y<m.length;y++){var b=g.childNodes[2*y];b||(y!=0&&g.appendChild(s.createTextNode("\u00a0|\u00a0")),b=s.buildDom(["a"],g)),b.textContent=m[y].title,b.lensCommand=m[y]}while(g.childNodes.length>2*y-1)g.lastChild.remove();var w=t.$cursorLayer.getPixelPosition({row:d,column:0},!0).top-l.lineHeight*v.rowsAbove-l.offset;g.style.top=w+"px";var E=t.gutterWidth,S=r.getLine(d).search(/\S|$/);S==-1&&(S=0),E+=S*l.characterWidth,g.style.paddingLeft=c+E+"px"}while(h<a.length)a.pop().remove()}function a(e){if(!e.lineWidgets)return;var t=e.widgetManager;e.lineWidgets.forEach(function(e){e&&e.lenses&&t.removeLineWidget(e)})}function f(e){e.codeLensProviders=[],e.renderer.on("afterRender",u),e.$codeLensClickHandler||(e.$codeLensClickHandler=function(t){var n=t.target.lensCommand;if(!n)return;e.execCommand(n.id,n.arguments),e._emit("codeLensClick",t)},r.addListener(e.container,"click",e.$codeLensClickHandler,e)),e.$updateLenses=function(){function s(){var r=n.selection.cursor,s=n.documentToScreenRow(r),o=n.getScrollTop(),u=t.setLenses(n,i),a=n.$undoManager&&n.$undoManager.$lastDelta;if(a&&a.action=="remove"&&a.lines.length>1)return;var f=n.documentToScreenRow(r),l=e.renderer.layerConfig.lineHeight,c=n.getScrollTop()+(f-s)*l;u==0&&o<l/4&&o>-l/4&&(c=-l),n.setScrollTop(c)}var n=e.session;if(!n)return;var r=e.codeLensProviders.length,i=[];e.codeLensProviders.forEach(function(e){e.provideCodeLenses(n,function(e,t){if(e)return;t.forEach(function(e){i.push(e)}),r--,r==0&&s()})})};var n=i.delayedCall(e.$updateLenses);e.$updateLensesOnInput=function(){n.delay(250)},e.on("input",e.$updateLensesOnInput)}function l(e){e.off("input",e.$updateLensesOnInput),e.renderer.off("afterRender",u),e.$codeLensClickHandler&&e.container.removeEventListener("click",e.$codeLensClickHandler)}var r=e("../lib/event"),i=e("../lib/lang"),s=e("../lib/dom");t.setLenses=function(e,t){var n=Number.MAX_VALUE;return a(e),t&&t.forEach(function(t){var r=t.start.row,i=t.start.column,s=e.lineWidgets&&e.lineWidgets[r];if(!s||!s.lenses)s=e.widgetManager.$registerLineWidget({rowCount:1,rowsAbove:1,row:r,column:i,lenses:[]});s.lenses.push(t.command),r<n&&(n=r)}),e._emit("changeFold",{data:{start:{row:n}}}),n},t.registerCodeLensProvider=function(e,t){e.setOption("enableCodeLens",!0),e.codeLensProviders.push(t),e.$updateLensesOnInput()},t.clear=function(e){t.setLenses(e,null)};var c=e("../editor").Editor;e("../config").defineOptions(c.prototype,"editor",{enableCodeLens:{set:function(e){e?f(this):l(this)}}}),s.importCssString("\n.ace_codeLens {\n    position: absolute;\n    color: #aaa;\n    font-size: 88%;\n    background: inherit;\n    width: 100%;\n    display: flex;\n    align-items: flex-end;\n    pointer-events: none;\n}\n.ace_codeLens > a {\n    cursor: pointer;\n    pointer-events: auto;\n}\n.ace_codeLens > a:hover {\n    color: #0000ff;\n    text-decoration: underline;\n}\n.ace_dark > .ace_codeLens > a:hover {\n    color: #4e94ce;\n}\n","codelense.css",!1)});                (function() {
                    window.require(["ace/ext/code_lens"], function(m) {
                        if (typeof module == "object" && typeof exports == "object" && module) {
                            module.exports = m;
                        }
                    });
                })();
            