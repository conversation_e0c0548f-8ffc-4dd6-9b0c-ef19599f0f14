define("ace/mode/doc_comment_highlight_rules",["require","exports","module","ace/lib/oop","ace/mode/text_highlight_rules"],function(e,t,n){"use strict";var r=e("../lib/oop"),i=e("./text_highlight_rules").TextHighlightRules,s=function(){this.$rules={start:[{token:"comment.doc.tag",regex:"@\\w+(?=\\s|$)"},s.getTagRule(),{defaultToken:"comment.doc.body",caseInsensitive:!0}]}};r.inherits(s,i),s.getTagRule=function(e){return{token:"comment.doc.tag.storage.type",regex:"\\b(?:TODO|FIXME|XXX|HACK)\\b"}},s.getStartRule=function(e){return{token:"comment.doc",regex:/\/\*\*(?!\/)/,next:e}},s.getEndRule=function(e){return{token:"comment.doc",regex:"\\*\\/",next:e}},t.DocCommentHighlightRules=s}),define("ace/mode/c_cpp_highlight_rules",["require","exports","module","ace/lib/oop","ace/mode/doc_comment_highlight_rules","ace/mode/text_highlight_rules"],function(e,t,n){"use strict";var r=e("../lib/oop"),i=e("./doc_comment_highlight_rules").DocCommentHighlightRules,s=e("./text_highlight_rules").TextHighlightRules,o=t.cFunctions="hypot|hypotf|hypotl|sscanf|system|snprintf|scanf|scalbn|scalbnf|scalbnl|scalbln|scalblnf|scalblnl|sin|sinh|sinhf|sinhl|sinf|sinl|signal|signbit|strstr|strspn|strncpy|strncat|strncmp|strcspn|strchr|strcoll|strcpy|strcat|strcmp|strtoimax|strtod|strtoul|strtoull|strtoumax|strtok|strtof|strtol|strtold|strtoll|strerror|strpbrk|strftime|strlen|strrchr|strxfrm|sprintf|setjmp|setvbuf|setlocale|setbuf|sqrt|sqrtf|sqrtl|swscanf|swprintf|srand|nearbyint|nearbyintf|nearbyintl|nexttoward|nexttowardf|nexttowardl|nextafter|nextafterf|nextafterl|nan|nanf|nanl|csin|csinh|csinhf|csinhl|csinf|csinl|csqrt|csqrtf|csqrtl|ccos|ccosh|ccoshf|ccosf|ccosl|cimag|cimagf|cimagl|ctime|ctan|ctanh|ctanhf|ctanhl|ctanf|ctanl|cos|cosh|coshf|coshl|cosf|cosl|conj|conjf|conjl|copysign|copysignf|copysignl|cpow|cpowf|cpowl|cproj|cprojf|cprojl|ceil|ceilf|ceill|cexp|cexpf|cexpl|clock|clog|clogf|clogl|clearerr|casin|casinh|casinhf|casinhl|casinf|casinl|cacos|cacosh|cacoshf|cacoshl|cacosf|cacosl|catan|catanh|catanhf|catanhl|catanf|catanl|calloc|carg|cargf|cargl|cabs|cabsf|cabsl|creal|crealf|creall|cbrt|cbrtf|cbrtl|time|toupper|tolower|tan|tanh|tanhf|tanhl|tanf|tanl|trunc|truncf|truncl|tgamma|tgammaf|tgammal|tmpnam|tmpfile|isspace|isnormal|isnan|iscntrl|isinf|isdigit|isunordered|isupper|ispunct|isprint|isfinite|iswspace|iswcntrl|iswctype|iswdigit|iswupper|iswpunct|iswprint|iswlower|iswalnum|iswalpha|iswgraph|iswxdigit|iswblank|islower|isless|islessequal|islessgreater|isalnum|isalpha|isgreater|isgreaterequal|isgraph|isxdigit|isblank|ilogb|ilogbf|ilogbl|imaxdiv|imaxabs|div|difftime|_Exit|ungetc|ungetwc|pow|powf|powl|puts|putc|putchar|putwc|putwchar|perror|printf|erf|erfc|erfcf|erfcl|erff|erfl|exit|exp|exp2|exp2f|exp2l|expf|expl|expm1|expm1f|expm1l|vsscanf|vsnprintf|vscanf|vsprintf|vswscanf|vswprintf|vprintf|vfscanf|vfprintf|vfwscanf|vfwprintf|vwscanf|vwprintf|va_start|va_copy|va_end|va_arg|qsort|fscanf|fsetpos|fseek|fclose|ftell|fopen|fdim|fdimf|fdiml|fpclassify|fputs|fputc|fputws|fputwc|fprintf|feholdexcept|fesetenv|fesetexceptflag|fesetround|feclearexcept|fetestexcept|feof|feupdateenv|feraiseexcept|ferror|fegetenv|fegetexceptflag|fegetround|fflush|fwscanf|fwide|fwprintf|fwrite|floor|floorf|floorl|fabs|fabsf|fabsl|fgets|fgetc|fgetpos|fgetws|fgetwc|freopen|free|fread|frexp|frexpf|frexpl|fmin|fminf|fminl|fmod|fmodf|fmodl|fma|fmaf|fmal|fmax|fmaxf|fmaxl|ldiv|ldexp|ldexpf|ldexpl|longjmp|localtime|localeconv|log|log1p|log1pf|log1pl|log10|log10f|log10l|log2|log2f|log2l|logf|logl|logb|logbf|logbl|labs|lldiv|llabs|llrint|llrintf|llrintl|llround|llroundf|llroundl|lrint|lrintf|lrintl|lround|lroundf|lroundl|lgamma|lgammaf|lgammal|wscanf|wcsstr|wcsspn|wcsncpy|wcsncat|wcsncmp|wcscspn|wcschr|wcscoll|wcscpy|wcscat|wcscmp|wcstoimax|wcstod|wcstoul|wcstoull|wcstoumax|wcstok|wcstof|wcstol|wcstold|wcstoll|wcstombs|wcspbrk|wcsftime|wcslen|wcsrchr|wcsrtombs|wcsxfrm|wctob|wctomb|wcrtomb|wprintf|wmemset|wmemchr|wmemcpy|wmemcmp|wmemmove|assert|asctime|asin|asinh|asinhf|asinhl|asinf|asinl|acos|acosh|acoshf|acoshl|acosf|acosl|atoi|atof|atol|atoll|atexit|atan|atanh|atanhf|atanhl|atan2|atan2f|atan2l|atanf|atanl|abs|abort|gets|getc|getchar|getenv|getwc|getwchar|gmtime|rint|rintf|rintl|round|roundf|roundl|rename|realloc|rewind|remove|remquo|remquof|remquol|remainder|remainderf|remainderl|rand|raise|bsearch|btowc|modf|modff|modfl|memset|memchr|memcpy|memcmp|memmove|mktime|malloc|mbsinit|mbstowcs|mbsrtowcs|mbtowc|mblen|mbrtowc|mbrlen",u=function(e){var t="break|case|continue|default|do|else|for|goto|if|_Pragma|return|switch|while|catch|operator|try|throw|using",n="asm|__asm__|auto|bool|_Bool|char|_Complex|double|enum|float|_Imaginary|int|int8_t|int16_t|int32_t|int64_t|long|short|signed|size_t|struct|typedef|uint8_t|uint16_t|uint32_t|uint64_t|union|unsigned|void|class|wchar_t|template|char16_t|char32_t",r="const|extern|register|restrict|static|volatile|inline|private|protected|public|friend|explicit|virtual|export|mutable|typename|constexpr|new|delete|alignas|alignof|decltype|noexcept|thread_local",s="and|and_eq|bitand|bitor|compl|not|not_eq|or|or_eq|typeid|xor|xor_eq|const_cast|dynamic_cast|reinterpret_cast|static_cast|sizeof|namespace",u="NULL|true|false|TRUE|FALSE|nullptr",a=this.$keywords=this.createKeywordMapper(Object.assign({"keyword.control":t,"storage.type":n,"storage.modifier":r,"keyword.operator":s,"variable.language":"this","constant.language":u,"support.function.C99.c":o},e),"identifier"),f="[a-zA-Z\\$_\u00a1-\uffff][a-zA-Z\\d\\$_\u00a1-\uffff]*\\b",l=/\\(?:['"?\\abfnrtv]|[0-7]{1,3}|x[a-fA-F\d]{2}|u[a-fA-F\d]{4}U[a-fA-F\d]{8}|.)/.source,c="%"+/(\d+\$)?/.source+/[#0\- +']*/.source+/[,;:_]?/.source+/((-?\d+)|\*(-?\d+\$)?)?/.source+/(\.((-?\d+)|\*(-?\d+\$)?)?)?/.source+/(hh|h|ll|l|j|t|z|q|L|vh|vl|v|hv|hl)?/.source+/(\[[^"\]]+\]|[diouxXDOUeEfFgGaACcSspn%])/.source;this.$rules={start:[{token:"comment",regex:"//$",next:"start"},{token:"comment",regex:"//",next:"singleLineComment"},i.getStartRule("doc-start"),{token:"comment",regex:"\\/\\*",next:"comment"},{token:"string",regex:"'(?:"+l+"|.)?'"},{token:"string.start",regex:'"',stateName:"qqstring",next:[{token:"string",regex:/\\\s*$/,next:"qqstring"},{token:"constant.language.escape",regex:l},{token:"constant.language.escape",regex:c},{token:"string.end",regex:'"|$',next:"start"},{defaultToken:"string"}]},{token:"string.start",regex:'R"\\(',stateName:"rawString",next:[{token:"string.end",regex:'\\)"',next:"start"},{defaultToken:"string"}]},{token:"constant.numeric",regex:"0[xX][0-9a-fA-F]+(L|l|UL|ul|u|U|F|f|ll|LL|ull|ULL)?\\b"},{token:"constant.numeric",regex:"[+-]?\\d+(?:(?:\\.\\d*)?(?:[eE][+-]?\\d+)?)?(L|l|UL|ul|u|U|F|f|ll|LL|ull|ULL)?\\b"},{token:"keyword",regex:"#\\s*(?:include|import|pragma|line|define|undef)\\b",next:"directive"},{token:"keyword",regex:"#\\s*(?:endif|if|ifdef|else|elif|ifndef)\\b"},{token:a,regex:"[a-zA-Z_$][a-zA-Z0-9_$]*"},{token:"keyword.operator",regex:/--|\+\+|<<=|>>=|>>>=|<>|&&|\|\||\?:|[*%\/+\-&\^|~!<>=]=?/},{token:"punctuation.operator",regex:"\\?|\\:|\\,|\\;|\\."},{token:"paren.lparen",regex:"[[({]"},{token:"paren.rparen",regex:"[\\])}]"},{token:"text",regex:"\\s+"}],comment:[{token:"comment",regex:"\\*\\/",next:"start"},{defaultToken:"comment"}],singleLineComment:[{token:"comment",regex:/\\$/,next:"singleLineComment"},{token:"comment",regex:/$/,next:"start"},{defaultToken:"comment"}],directive:[{token:"constant.other.multiline",regex:/\\/},{token:"constant.other.multiline",regex:/.*\\/},{token:"constant.other",regex:"\\s*<.+?>",next:"start"},{token:"constant.other",regex:'\\s*["](?:(?:\\\\.)|(?:[^"\\\\]))*?["]',next:"start"},{token:"constant.other",regex:"\\s*['](?:(?:\\\\.)|(?:[^'\\\\]))*?[']",next:"start"},{token:"constant.other",regex:/[^\\\/]+/,next:"start"}]},this.embedRules(i,"doc-",[i.getEndRule("start")]),this.normalizeRules()};r.inherits(u,s),t.c_cppHighlightRules=u}),define("ace/mode/objectivec_highlight_rules",["require","exports","module","ace/lib/oop","ace/mode/doc_comment_highlight_rules","ace/mode/c_cpp_highlight_rules"],function(e,t,n){"use strict";var r=e("../lib/oop"),i=e("./doc_comment_highlight_rules").DocCommentHighlightRules,s=e("./c_cpp_highlight_rules").c_cppHighlightRules,o=function(){var e={"support.function.cocoa.leopard":"NSRectToCGRect|NSRectFromCGRect|NSMakeCollectable|NSStringFromProtocol|NSSizeToCGSize|NSSizeFromCGSize|NSDrawNinePartImage|NSDrawThreePartImage|NSPointToCGPoint|NSPointFromCGPoint|NSProtocolFromString|NSEventMaskFromType|NSValue","support.function.cocoa":"NSRoundDownToMultipleOfPageSize|NSRoundUpToMultipleOfPageSize|NSRunCriticalAlertPanel|NSRunCriticalAlertPanelRelativeToWindow|NSRunInformationalAlertPanel|NSRunInformationalAlertPanelRelativeToWindow|NSRunAlertPanel|NSRunAlertPanelRelativeToWindow|NSResetMapTable|NSResetHashTable|NSRecycleZone|NSRectClip|NSRectClipList|NSRectFill|NSRectFillUsingOperation|NSRectFillList|NSRectFillListUsingOperation|NSRectFillListWithGrays|NSRectFillListWithColors|NSRectFillListWithColorsUsingOperation|NSRectFromString|NSRecordAllocationEvent|NSReturnAddress|NSReleaseAlertPanel|NSReadPixel|NSRealMemoryAvailable|NSReallocateCollectable|NSRegisterServicesProvider|NSRangeFromString|NSGetSizeAndAlignment|NSGetCriticalAlertPanel|NSGetInformationalAlertPanel|NSGetUncaughtExceptionHandler|NSGetFileType|NSGetFileTypes|NSGetWindowServerMemory|NSGetAlertPanel|NSMinX|NSMinY|NSMidX|NSMidY|NSMouseInRect|NSMapRemove|NSMapGet|NSMapMember|NSMapInsert|NSMapInsertIfAbsent|NSMapInsertKnownAbsent|NSMakeRect|NSMakeRange|NSMakeSize|NSMakePoint|NSMaxRange|NSMaxX|NSMaxY|NSBitsPerSampleFromDepth|NSBitsPerPixelFromDepth|NSBestDepth|NSBeep|NSBeginCriticalAlertSheet|NSBeginInformationalAlertSheet|NSBeginAlertSheet|NSShouldRetainWithZone|NSShowsServicesMenuItem|NSShowAnimationEffect|NSStringFromRect|NSStringFromRange|NSStringFromMapTable|NSStringFromSize|NSStringFromSelector|NSStringFromHashTable|NSStringFromClass|NSStringFromPoint|NSSizeFromString|NSSetShowsServicesMenuItem|NSSetZoneName|NSSetUncaughtExceptionHandler|NSSetFocusRingStyle|NSSelectorFromString|NSSearchPathForDirectoriesInDomains|NSSwapBigShortToHost|NSSwapBigIntToHost|NSSwapBigDoubleToHost|NSSwapBigFloatToHost|NSSwapBigLongToHost|NSSwapBigLongLongToHost|NSSwapShort|NSSwapHostShortToBig|NSSwapHostShortToLittle|NSSwapHostIntToBig|NSSwapHostIntToLittle|NSSwapHostDoubleToBig|NSSwapHostDoubleToLittle|NSSwapHostFloatToBig|NSSwapHostFloatToLittle|NSSwapHostLongToBig|NSSwapHostLongToLittle|NSSwapHostLongLongToBig|NSSwapHostLongLongToLittle|NSSwapInt|NSSwapDouble|NSSwapFloat|NSSwapLittleShortToHost|NSSwapLittleIntToHost|NSSwapLittleDoubleToHost|NSSwapLittleFloatToHost|NSSwapLittleLongToHost|NSSwapLittleLongLongToHost|NSSwapLong|NSSwapLongLong|NSHighlightRect|NSHostByteOrder|NSHomeDirectory|NSHomeDirectoryForUser|NSHeight|NSHashRemove|NSHashGet|NSHashInsert|NSHashInsertIfAbsent|NSHashInsertKnownAbsent|NSHFSTypeCodeFromFileType|NSHFSTypeOfFile|NSNumberOfColorComponents|NSNextMapEnumeratorPair|NSNextHashEnumeratorItem|NSContainsRect|NSConvertGlyphsToPackedGlyphs|NSConvertSwappedDoubleToHost|NSConvertSwappedFloatToHost|NSConvertHostDoubleToSwapped|NSConvertHostFloatToSwapped|NSCountMapTable|NSCountHashTable|NSCountFrames|NSCountWindows|NSCountWindowsForContext|NSCopyMemoryPages|NSCopyMapTableWithZone|NSCopyBits|NSCopyHashTableWithZone|NSCopyObject|NSColorSpaceFromDepth|NSCompareMapTables|NSCompareHashTables|NSClassFromString|NSCreateMapTable|NSCreateMapTableWithZone|NSCreateHashTable|NSCreateHashTableWithZone|NSCreateZone|NSCreateFilenamePboardType|NSCreateFileContentsPboardType|NSTemporaryDirectory|NSIsControllerMarker|NSIsEmptyRect|NSIsFreedObject|NSInsetRect|NSIncrementExtraRefCount|NSIntersectsRect|NSIntersectionRect|NSIntersectionRange|NSInterfaceStyleForKey|NSIntegralRect|NSZoneRealloc|NSZoneMalloc|NSZoneName|NSZoneCalloc|NSZoneFromPointer|NSZoneFree|NSOpenStepRootDirectory|NSOffsetRect|NSDisableScreenUpdates|NSDivideRect|NSDottedFrameRect|NSDecimalRound|NSDecimalMultiply|NSDecimalString|NSDecimalSubtract|NSDecimalNormalize|NSDecimalCopy|NSDecimalCompact|NSDecimalCompare|NSDecimalIsNotANumber|NSDecimalDivide|NSDecimalPower|NSDecimalAdd|NSDecrementExtraRefCountWasZero|NSDefaultMallocZone|NSDeallocateMemoryPages|NSDeallocateObject|NSDrawGroove|NSDrawGrayBezel|NSDrawBitmap|NSDrawButton|NSDrawColorTiledRects|NSDrawTiledRects|NSDrawDarkBezel|NSDrawWhiteBezel|NSDrawWindowBackground|NSDrawLightBezel|NSUserName|NSUnionRect|NSUnionRange|NSUnregisterServicesProvider|NSUpdateDynamicServices|NSJavaBundleSetup|NSJavaBundleCleanup|NSJavaSetup|NSJavaSetupVirtualMachine|NSJavaNeedsToLoadClasses|NSJavaNeedsVirtualMachine|NSJavaClassesForBundle|NSJavaClassesFromPath|NSJavaObjectNamedInPath|NSJavaProvidesClasses|NSPointInRect|NSPointFromString|NSPerformService|NSPlanarFromDepth|NSPageSize|NSEndMapTableEnumeration|NSEndHashTableEnumeration|NSEnumerateMapTable|NSEnumerateHashTable|NSEnableScreenUpdates|NSEqualRects|NSEqualRanges|NSEqualSizes|NSEqualPoints|NSEraseRect|NSExtraRefCount|NSFileTypeForHFSTypeCode|NSFullUserName|NSFreeMapTable|NSFreeHashTable|NSFrameRect|NSFrameRectWithWidth|NSFrameRectWithWidthUsingOperation|NSFrameAddress|NSWindowList|NSWindowListForContext|NSWidth|NSLocationInRange|NSLog|NSLogv|NSLogPageSize|NSAccessibilityRoleDescription|NSAccessibilityRoleDescriptionForUIElement|NSAccessibilityRaiseBadArgumentException|NSAccessibilityUnignoredChildren|NSAccessibilityUnignoredChildrenForOnlyChild|NSAccessibilityUnignoredDescendant|NSAccessibilityUnignoredAncestor|NSAccessibilityPostNotification|NSAccessibilityActionDescription|NSApplicationMain|NSApplicationLoad|NSAvailableWindowDepths|NSAllMapTableValues|NSAllMapTableKeys|NSAllHashTableObjects|NSAllocateMemoryPages|NSAllocateCollectable|NSAllocateObject","support.class.cocoa.leopard":"NSRuleEditor|NSGarbageCollector|NSGradient|NSMapTable|NSHashTable|NSCondition|NSCollectionView|NSCollectionViewItem|NSToolbarItemGroup|NSTextInputClient|NSTreeNode|NSTrackingArea|NSInvocationOperation|NSOperation|NSOperationQueue|NSDictionaryController|NSDockTile|NSPointerFunctions|NSPointerArray|NSPathControl|NSPathControlDelegate|NSPathComponentCell|NSPathCell|NSPathCellDelegate|NSPrintPanelAccessorizing|NSPredicateEditor|NSPredicateEditorRowTemplate|NSViewController|NSFastEnumeration|NSAnimationContext|NSAnimatablePropertyContainer","support.class.cocoa":"NSRunLoop|NSRulerMarker|NSRulerView|NSResponder|NSRecursiveLock|NSRelativeSpecifier|NSRandomSpecifier|NSRangeSpecifier|NSGetCommand|NSGlyphGenerator|NSGlyphStorage|NSGlyphInfo|NSGraphicsContext|NSXMLNode|NSXMLDocument|NSXMLDTD|NSXMLDTDNode|NSXMLParser|NSXMLElement|NSMiddleSpecifier|NSMovie|NSMovieView|NSMoveCommand|NSMutableString|NSMutableSet|NSMutableCharacterSet|NSMutableCopying|NSMutableIndexSet|NSMutableDictionary|NSMutableData|NSMutableURLRequest|NSMutableParagraphStyle|NSMutableAttributedString|NSMutableArray|NSMessagePort|NSMessagePortNameServer|NSMenu|NSMenuItem|NSMenuItemCell|NSMenuView|NSMethodSignature|NSMetadataItem|NSMetadataQuery|NSMetadataQueryResultGroup|NSMetadataQueryAttributeValueTuple|NSMachBootstrapServer|NSMachPort|NSMatrix|NSBitmapImageRep|NSBox|NSBundle|NSButton|NSButtonCell|NSBezierPath|NSBrowser|NSBrowserCell|NSShadow|NSScanner|NSScriptSuiteRegistry|NSScriptCoercionHandler|NSScriptCommand|NSScriptCommandDescription|NSScriptClassDescription|NSScriptObjectSpecifier|NSScriptExecutionContext|NSScriptWhoseTest|NSScroller|NSScrollView|NSScreen|NSStepper|NSStepperCell|NSStatusBar|NSStatusItem|NSString|NSStream|NSSimpleHorizontalTypesetter|NSSimpleCString|NSSocketPort|NSSocketPortNameServer|NSSound|NSSortDescriptor|NSSpecifierTest|NSSpeechRecognizer|NSSpeechSynthesizer|NSSpellServer|NSSpellChecker|NSSplitView|NSSecureTextField|NSSecureTextFieldCell|NSSet|NSSetCommand|NSSearchField|NSSearchFieldCell|NSSerializer|NSSegmentedControl|NSSegmentedCell|NSSlider|NSSliderCell|NSSavePanel|NSHost|NSHTTPCookie|NSHTTPCookieStorage|NSHTTPURLResponse|NSHelpManager|NSNib|NSNibConnector|NSNibControlConnector|NSNibOutletConnector|NSNotification|NSNotificationCenter|NSNotificationQueue|NSNull|NSNumber|NSNumberFormatter|NSNetService|NSNetServiceBrowser|NSNameSpecifier|NSChangeSpelling|NSCharacterSet|NSConstantString|NSConnection|NSControl|NSController|NSConditionLock|NSCoding|NSCoder|NSCountCommand|NSCountedSet|NSCopying|NSColor|NSColorSpace|NSColorPickingCustom|NSColorPickingDefault|NSColorPicker|NSColorPanel|NSColorWell|NSColorList|NSCompoundPredicate|NSComparisonPredicate|NSComboBox|NSComboBoxCell|NSCustomImageRep|NSCursor|NSCIImageRep|NSCell|NSClipView|NSCloseCommand|NSCloneCommand|NSClassDescription|NSCachedImageRep|NSCachedURLResponse|NSCalendar|NSCalendarDate|NSCreateCommand|NSThread|NSTypesetter|NSTimeZone|NSTimer|NSToolbar|NSToolbarItem|NSToolbarItemValidations|NSTokenField|NSTokenFieldCell|NSText|NSTextBlock|NSTextStorage|NSTextContainer|NSTextTab|NSTextTable|NSTextTableBlock|NSTextInput|NSTextView|NSTextField|NSTextFieldCell|NSTextList|NSTextAttachment|NSTextAttachmentCell|NSTask|NSTableHeaderCell|NSTableHeaderView|NSTableColumn|NSTableView|NSTabView|NSTabViewItem|NSTreeController|NSIndexSpecifier|NSIndexSet|NSIndexPath|NSInputManager|NSInputStream|NSInputServiceProvider|NSInputServer|NSInputServerMouseTracker|NSInvocation|NSIgnoreMisspelledWords|NSImage|NSImageRep|NSImageCell|NSImageView|NSOutputStream|NSOutlineView|NSOpenGLContext|NSOpenGLPixelBuffer|NSOpenGLPixelFormat|NSOpenGLView|NSOpenPanel|NSObjCTypeSerializationCallBack|NSObject|NSObjectController|NSDistantObject|NSDistantObjectRequest|NSDistributedNotificationCenter|NSDistributedLock|NSDictionary|NSDirectoryEnumerator|NSDocument|NSDocumentController|NSDeserializer|NSDecimalNumber|NSDecimalNumberBehaviors|NSDecimalNumberHandler|NSDeleteCommand|NSDate|NSDateComponents|NSDatePicker|NSDatePickerCell|NSDateFormatter|NSData|NSDrawer|NSDraggingInfo|NSUserInterfaceValidations|NSUserDefaults|NSUserDefaultsController|NSURL|NSURLResponse|NSURLRequest|NSURLHandle|NSURLHandleClient|NSURLConnection|NSURLCache|NSURLCredential|NSURLCredentialStorage|NSURLDownload|NSURLDownloadDelegate|NSURLProtocol|NSURLProtocolClient|NSURLProtectionSpace|NSURLAuthenticationChallenge|NSURLAuthenticationChallengeSender|NSUniqueIDSpecifier|NSUndoManager|NSUnarchiver|NSPipe|NSPositionalSpecifier|NSPopUpButton|NSPopUpButtonCell|NSPort|NSPortMessage|NSPortNameServer|NSPortCoder|NSPICTImageRep|NSPersistentDocument|NSPDFImageRep|NSPasteboard|NSPanel|NSParagraphStyle|NSPageLayout|NSPrintInfo|NSPrinter|NSPrintOperation|NSPrintPanel|NSProcessInfo|NSProtocolChecker|NSPropertySpecifier|NSPropertyListSerialization|NSProgressIndicator|NSProxy|NSPredicate|NSEnumerator|NSEvent|NSEPSImageRep|NSError|NSException|NSExistsCommand|NSExpression|NSView|NSViewAnimation|NSValidatedToobarItem|NSValidatedUserInterfaceItem|NSValueTransformer|NSKeyedUnarchiver|NSKeyedArchiver|NSQuickDrawView|NSQuitCommand|NSFileManager|NSFileHandle|NSFileWrapper|NSFont|NSFontManager|NSFontDescriptor|NSFontPanel|NSFormCell|NSFormatter|NSWhoseSpecifier|NSWindow|NSWindowController|NSWorkspace|NSLock|NSLocking|NSLocale|NSLogicalTest|NSLevelIndicator|NSLevelIndicatorCell|NSLayoutManager|NSAssertionHandler|NSAnimation|NSActionCell|NSAttributedString|NSAutoreleasePool|NSATSTypesetter|NSApplication|NSAppleScript|NSAppleEventManager|NSAppleEventDescriptor|NSAffineTransform|NSAlert|NSArchiver|NSArray|NSArrayController","support.type.cocoa.leopard":"","support.class.quartz":"CISampler|CIContext|CIColor|CIImage|CIImageAccumulator|CIPlugIn|CIPlugInRegistration|CIVector|CIKernel|CIFilter|CIFilterGenerator|CIFilterShape|CARenderer|CAMediaTiming|CAMediaTimingFunction|CABasicAnimation|CAScrollLayer|CAConstraint|CAConstraintLayoutManager|CATiledLayer|CATextLayer|CATransition|CATransaction|CAOpenGLLayer|CAPropertyAnimation|CAKeyframeAnimation|CALayer|CAAnimation|CAAnimationGroup|CAAction","support.type.quartz":"CGFloat|CGPoint|CGSize|CGRect|CIFormat|CAConstraintAttribute","support.type.cocoa":"NSRect|NSRectEdge|NSRange|NSGlyph|NSGlyphRelation|NSGlyphLayoutMode|NSGradientType|NSModalSession|NSMatrixMode|NSMapEnumerator|NSBitmapImageFileType|NSBorderType|NSButtonType|NSBezelStyle|NSBackingStoreType|NSBrowserColumnResizingType|NSScrollerPart|NSScrollerArrow|NSScrollArrowPosition|NSScreenAuxiliaryOpaque|NSStringEncoding|NSSize|NSSocketNativeHandle|NSSelectionGranularity|NSSelectionDirection|NSSelectionAffinity|NSSwappedDouble|NSSwappedFloat|NSSaveOperationType|NSHashEnumerator|NSHandler|NSHandler2|NSControlSize|NSControlTint|NSCompositingOperation|NSComparisonResult|NSCellState|NSCellType|NSCellImagePosition|NSCellAttribute|NSThreadPrivate|NSTypesetterGlyphInfo|NSTickMarkPosition|NSTitlePosition|NSTimeInterval|NSToolTipTag|NSToolbarSizeMode|NSToolbarDisplayMode|NSTokenStyle|NSTIFFCompression|NSTextTabType|NSTextAlignment|NSTabState|NSTableViewDropOperation|NSTabViewType|NSTrackingRectTag|NSImageInterpolation|NSZone|NSOpenGLContextAuxiliary|NSOpenGLPixelFormatAuxiliary|NSDocumentChangeType|NSDatePickerElementFlags|NSDrawerState|NSDragOperation|NSUsableScrollerParts|NSPoint|NSPrintingPageOrder|NSProgressIndicatorStyle|NSProgressIndicatorThickness|NSProgressIndicatorThreadInfo|NSEventType|NSKeyValueObservingOptions|NSFontSymbolicTraits|NSFontTraitMask|NSFontAction|NSFocusRingType|NSWindowOrderingMode|NSWindowDepth|NSWorkspaceIconCreationOptions|NSWorkspaceLaunchOptions|NSWritingDirection|NSLineBreakMode|NSLayoutStatus|NSLayoutDirection|NSAnimationProgress|NSAnimationEffect|NSApplicationTerminateReply|NSApplicationDelegateReply|NSApplicationPrintReply|NSAppleEventManagerSuspensionID|NSAffineTransformStruct|NSAlertStyle","support.constant.cocoa":"NSRGBModeColorPanel|NSRGBColorSpaceModel|NSRightMouseDown|NSRightMouseDownMask|NSRightMouseDragged|NSRightMouseDraggedMask|NSRightMouseUp|NSRightMouseUpMask|NSRightTextMovement|NSRightTextAlignment|NSRightTabsBezelBorder|NSRightTabStopType|NSRightArrowFunctionKey|NSRoundRectBezelStyle|NSRoundBankers|NSRoundedBezelStyle|NSRoundedTokenStyle|NSRoundedDisclosureBezelStyle|NSRoundDown|NSRoundUp|NSRoundPlain|NSRoundLineCapStyle|NSRoundLineJoinStyle|NSRunStoppedResponse|NSRunContinuesResponse|NSRunAbortedResponse|NSResizableWindowMask|NSResetCursorRectsRunLoopOrdering|NSResetFunctionKey|NSRecessedBezelStyle|NSReceiversCantHandleCommandScriptError|NSReceiverEvaluationScriptError|NSReturnTextMovement|NSRedoFunctionKey|NSRequiredArgumentsMissingScriptError|NSRelevancyLevelIndicatorStyle|NSRelativeBefore|NSRelativeAfter|NSRegularSquareBezelStyle|NSRegularControlSize|NSRemoveTraitFontAction|NSRandomSubelement|NSRangeDateMode|NSRatingLevelIndicatorStyle|NSRadioModeMatrix|NSRadioButton|NSGIFFileType|NSGlyphBelow|NSGlyphInscribeBelow|NSGlyphInscribeBase|NSGlyphInscribeOverstrike|NSGlyphInscribeOverBelow|NSGlyphInscribeAbove|NSGlyphLayoutWithPrevious|NSGlyphLayoutAtAPoint|NSGlyphLayoutAgainstAPoint|NSGlyphAttributeBidiLevel|NSGlyphAttributeSoft|NSGlyphAttributeInscribe|NSGlyphAttributeElastic|NSGlyphAbove|NSGrooveBorder|NSGreaterThanComparison|NSGreaterThanOrEqualToComparison|NSGreaterThanOrEqualToPredicateOperatorType|NSGreaterThanPredicateOperatorType|NSGrayModeColorPanel|NSGrayColorSpaceModel|NSGradientNone|NSGradientConcaveStrong|NSGradientConcaveWeak|NSGradientConvexStrong|NSGradientConvexWeak|NSGraphiteControlTint|NSXMLNotationDeclarationKind|NSXMLNodeCompactEmptyElement|NSXMLNodeIsCDATA|NSXMLNodeOptionsNone|NSXMLNodeUseSingleQuotes|NSXMLNodeUseDoubleQuotes|NSXMLNodePreserveNamespaceOrder|NSXMLNodePreserveCharacterReferences|NSXMLNodePreserveCDATA|NSXMLNodePreserveDTD|NSXMLNodePreservePrefixes|NSXMLNodePreserveEntities|NSXMLNodePreserveEmptyElements|NSXMLNodePreserveQuotes|NSXMLNodePreserveWhitespace|NSXMLNodePreserveAttributeOrder|NSXMLNodePreserveAll|NSXMLNodePrettyPrint|NSXMLNodeExpandEmptyElement|NSXMLNamespaceKind|NSXMLCommentKind|NSXMLTextKind|NSXMLInvalidKind|NSXMLDocumentXMLKind|NSXMLDocumentXHTMLKind|NSXMLDocumentXInclude|NSXMLDocumentHTMLKind|NSXMLDocumentTidyXML|NSXMLDocumentTidyHTML|NSXMLDocumentTextKind|NSXMLDocumentIncludeContentTypeDeclaration|NSXMLDocumentValidate|NSXMLDocumentKind|NSXMLDTDKind|NSXMLParserGTRequiredError|NSXMLParserXMLDeclNotStartedError|NSXMLParserXMLDeclNotFinishedError|NSXMLParserMisplacedXMLDeclarationError|NSXMLParserMisplacedCDATAEndStringError|NSXMLParserMixedContentDeclNotStartedError|NSXMLParserMixedContentDeclNotFinishedError|NSXMLParserStandaloneValueError|NSXMLParserStringNotStartedError|NSXMLParserStringNotClosedError|NSXMLParserSpaceRequiredError|NSXMLParserSeparatorRequiredError|NSXMLParserNMTOKENRequiredError|NSXMLParserNotationNotStartedError|NSXMLParserNotationNotFinishedError|NSXMLParserNotWellBalancedError|NSXMLParserNoDTDError|NSXMLParserNamespaceDeclarationError|NSXMLParserNAMERequiredError|NSXMLParserCharacterRefInDTDError|NSXMLParserCharacterRefInPrologError|NSXMLParserCharacterRefInEpilogError|NSXMLParserCharacterRefAtEOFError|NSXMLParserConditionalSectionNotStartedError|NSXMLParserConditionalSectionNotFinishedError|NSXMLParserCommentNotFinishedError|NSXMLParserCommentContainsDoubleHyphenError|NSXMLParserCDATANotFinishedError|NSXMLParserTagNameMismatchError|NSXMLParserInternalError|NSXMLParserInvalidHexCharacterRefError|NSXMLParserInvalidCharacterRefError|NSXMLParserInvalidCharacterInEntityError|NSXMLParserInvalidCharacterError|NSXMLParserInvalidConditionalSectionError|NSXMLParserInvalidDecimalCharacterRefError|NSXMLParserInvalidURIError|NSXMLParserInvalidEncodingNameError|NSXMLParserInvalidEncodingError|NSXMLParserOutOfMemoryError|NSXMLParserDocumentStartError|NSXMLParserDelegateAbortedParseError|NSXMLParserDOCTYPEDeclNotFinishedError|NSXMLParserURIRequiredError|NSXMLParserURIFragmentError|NSXMLParserUndeclaredEntityError|NSXMLParserUnparsedEntityError|NSXMLParserUnknownEncodingError|NSXMLParserUnfinishedTagError|NSXMLParserPCDATARequiredError|NSXMLParserPublicIdentifierRequiredError|NSXMLParserParsedEntityRefMissingSemiError|NSXMLParserParsedEntityRefNoNameError|NSXMLParserParsedEntityRefInInternalSubsetError|NSXMLParserParsedEntityRefInInternalError|NSXMLParserParsedEntityRefInPrologError|NSXMLParserParsedEntityRefInEpilogError|NSXMLParserParsedEntityRefAtEOFError|NSXMLParserProcessingInstructionNotStartedError|NSXMLParserProcessingInstructionNotFinishedError|NSXMLParserPrematureDocumentEndError|NSXMLParserEncodingNotSupportedError|NSXMLParserEntityRefInDTDError|NSXMLParserEntityRefInPrologError|NSXMLParserEntityRefInEpilogError|NSXMLParserEntityReferenceMissingSemiError|NSXMLParserEntityReferenceWithoutNameError|NSXMLParserEntityRefLoopError|NSXMLParserEntityRefAtEOFError|NSXMLParserEntityBoundaryError|NSXMLParserEntityNotStartedError|NSXMLParserEntityNotFinishedError|NSXMLParserEntityIsParameterError|NSXMLParserEntityIsExternalError|NSXMLParserEntityValueRequiredError|NSXMLParserEqualExpectedError|NSXMLParserElementContentDeclNotStartedError|NSXMLParserElementContentDeclNotFinishedError|NSXMLParserExternalStandaloneEntityError|NSXMLParserExternalSubsetNotFinishedError|NSXMLParserExtraContentError|NSXMLParserEmptyDocumentError|NSXMLParserLiteralNotStartedError|NSXMLParserLiteralNotFinishedError|NSXMLParserLTRequiredError|NSXMLParserLTSlashRequiredError|NSXMLParserLessThanSymbolInAttributeError|NSXMLParserAttributeRedefinedError|NSXMLParserAttributeHasNoValueError|NSXMLParserAttributeNotStartedError|NSXMLParserAttributeNotFinishedError|NSXMLParserAttributeListNotStartedError|NSXMLParserAttributeListNotFinishedError|NSXMLProcessingInstructionKind|NSXMLEntityGeneralKind|NSXMLEntityDeclarationKind|NSXMLEntityUnparsedKind|NSXMLEntityParsedKind|NSXMLEntityParameterKind|NSXMLEntityPredefined|NSXMLElementDeclarationMixedKind|NSXMLElementDeclarationUndefinedKind|NSXMLElementDeclarationElementKind|NSXMLElementDeclarationEmptyKind|NSXMLElementDeclarationKind|NSXMLElementDeclarationAnyKind|NSXMLElementKind|NSXMLAttributeNMTokensKind|NSXMLAttributeNMTokenKind|NSXMLAttributeNotationKind|NSXMLAttributeCDATAKind|NSXMLAttributeIDRefsKind|NSXMLAttributeIDRefKind|NSXMLAttributeIDKind|NSXMLAttributeDeclarationKind|NSXMLAttributeEntityKind|NSXMLAttributeEntitiesKind|NSXMLAttributeEnumerationKind|NSXMLAttributeKind|NSMinXEdge|NSMiniaturizableWindowMask|NSMinYEdge|NSMinuteCalendarUnit|NSMiterLineJoinStyle|NSMiddleSubelement|NSMixedState|NSMonthCalendarUnit|NSModeSwitchFunctionKey|NSMouseMoved|NSMouseMovedMask|NSMouseEntered|NSMouseEnteredMask|NSMouseEventSubtype|NSMouseExited|NSMouseExitedMask|NSMoveToBezierPathElement|NSMomentaryChangeButton|NSMomentaryPushButton|NSMomentaryPushInButton|NSMomentaryLight|NSMomentaryLightButton|NSMenuFunctionKey|NSMacintoshInterfaceStyle|NSMacOSRomanStringEncoding|NSMatchesPredicateOperatorType|NSMappedRead|NSMaxXEdge|NSMaxYEdge|NSMACHOperatingSystem|NSBMPFileType|NSBottomTabsBezelBorder|NSBoldFontMask|NSBorderlessWindowMask|NSBoxSecondary|NSBoxSeparator|NSBoxOldStyle|NSBoxPrimary|NSButtLineCapStyle|NSBezelBorder|NSBevelLineJoinStyle|NSBelowBottom|NSBelowTop|NSBeginsWithComparison|NSBeginsWithPredicateOperatorType|NSBeginFunctionKey|NSBlueControlTint|NSBackspaceCharacter|NSBacktabTextMovement|NSBackingStoreRetained|NSBackingStoreBuffered|NSBackingStoreNonretained|NSBackTabCharacter|NSBackwardsSearch|NSBackgroundTab|NSBrowserNoColumnResizing|NSBrowserUserColumnResizing|NSBrowserAutoColumnResizing|NSBreakFunctionKey|NSShiftJISStringEncoding|NSShiftKeyMask|NSShowControlGlyphs|NSShowInvisibleGlyphs|NSShadowlessSquareBezelStyle|NSSysReqFunctionKey|NSSystemDomainMask|NSSystemDefined|NSSystemDefinedMask|NSSystemFunctionKey|NSSymbolStringEncoding|NSScannedOption|NSScaleNone|NSScaleToFit|NSScaleProportionally|NSScrollerNoPart|NSScrollerIncrementPage|NSScrollerIncrementLine|NSScrollerIncrementArrow|NSScrollerDecrementPage|NSScrollerDecrementLine|NSScrollerDecrementArrow|NSScrollerKnob|NSScrollerKnobSlot|NSScrollerArrowsMinEnd|NSScrollerArrowsMaxEnd|NSScrollerArrowsNone|NSScrollerArrowsDefaultSetting|NSScrollWheel|NSScrollWheelMask|NSScrollLockFunctionKey|NSScreenChangedEventType|NSStopFunctionKey|NSStringDrawingOneShot|NSStringDrawingDisableScreenFontSubstitution|NSStringDrawingUsesDeviceMetrics|NSStringDrawingUsesFontLeading|NSStringDrawingUsesLineFragmentOrigin|NSStreamStatusReading|NSStreamStatusNotOpen|NSStreamStatusClosed|NSStreamStatusOpen|NSStreamStatusOpening|NSStreamStatusError|NSStreamStatusWriting|NSStreamStatusAtEnd|NSStreamEventHasBytesAvailable|NSStreamEventHasSpaceAvailable|NSStreamEventNone|NSStreamEventOpenCompleted|NSStreamEventEndEncountered|NSStreamEventErrorOccurred|NSSingleDateMode|NSSingleUnderlineStyle|NSSizeDownFontAction|NSSizeUpFontAction|NSSolarisOperatingSystem|NSSunOSOperatingSystem|NSSpecialPageOrder|NSSecondCalendarUnit|NSSelectByCharacter|NSSelectByParagraph|NSSelectByWord|NSSelectingNext|NSSelectingPrevious|NSSelectionAffinityDownstream|NSSelectionAffinityUpstream|NSSelectedTab|NSSelectFunctionKey|NSSegmentSwitchTrackingMomentary|NSSegmentSwitchTrackingSelectOne|NSSegmentSwitchTrackingSelectAny|NSSquareLineCapStyle|NSSwitchButton|NSSaveToOperation|NSSaveOptionsYes|NSSaveOptionsNo|NSSaveOptionsAsk|NSSaveOperation|NSSaveAsOperation|NSSmallSquareBezelStyle|NSSmallControlSize|NSSmallCapsFontMask|NSSmallIconButtonBezelStyle|NSHighlightModeMatrix|NSHSBModeColorPanel|NSHourMinuteSecondDatePickerElementFlag|NSHourMinuteDatePickerElementFlag|NSHourCalendarUnit|NSHorizontalRuler|NSHomeFunctionKey|NSHTTPCookieAcceptPolicyNever|NSHTTPCookieAcceptPolicyOnlyFromMainDocumentDomain|NSHTTPCookieAcceptPolicyAlways|NSHelpButtonBezelStyle|NSHelpKeyMask|NSHelpFunctionKey|NSHeavierFontAction|NSHPUXOperatingSystem|NSYearMonthDayDatePickerElementFlag|NSYearMonthDatePickerElementFlag|NSYearCalendarUnit|NSNonStandardCharacterSetFontMask|NSNonZeroWindingRule|NSNonactivatingPanelMask|NSNonLossyASCIIStringEncoding|NSNoBorder|NSNotificationSuspensionBehaviorHold|NSNotificationSuspensionBehaviorCoalesce|NSNotificationSuspensionBehaviorDeliverImmediately|NSNotificationSuspensionBehaviorDrop|NSNotificationNoCoalescing|NSNotificationCoalescingOnSender|NSNotificationCoalescingOnName|NSNotificationDeliverImmediately|NSNotificationPostToAllSessions|NSNotPredicateType|NSNotEqualToPredicateOperatorType|NSNoScriptError|NSNoScrollerParts|NSNoSubelement|NSNoSpecifierError|NSNoCellMask|NSNoTitle|NSNoTopLevelContainersSpecifierError|NSNoTabsBezelBorder|NSNoTabsNoBorder|NSNoTabsLineBorder|NSNoInterfaceStyle|NSNoImage|NSNoUnderlineStyle|NSNoFontChangeAction|NSNullGlyph|NSNullCellType|NSNumericSearch|NSNumericPadKeyMask|NSNumberFormatterRoundHalfDown|NSNumberFormatterRoundHalfUp|NSNumberFormatterRoundHalfEven|NSNumberFormatterRoundCeiling|NSNumberFormatterRoundDown|NSNumberFormatterRoundUp|NSNumberFormatterRoundFloor|NSNumberFormatterBehavior10|NSNumberFormatterBehaviorDefault|NSNumberFormatterScientificStyle|NSNumberFormatterSpellOutStyle|NSNumberFormatterNoStyle|NSNumberFormatterCurrencyStyle|NSNumberFormatterDecimalStyle|NSNumberFormatterPercentStyle|NSNumberFormatterPadBeforeSuffix|NSNumberFormatterPadBeforePrefix|NSNumberFormatterPadAfterSuffix|NSNumberFormatterPadAfterPrefix|NSNetServicesBadArgumentError|NSNetServicesNotFoundError|NSNetServicesCollisionError|NSNetServicesCancelledError|NSNetServicesTimeoutError|NSNetServicesInvalidError|NSNetServicesUnknownError|NSNetServicesActivityInProgress|NSNetworkDomainMask|NSNewlineCharacter|NSNextStepInterfaceStyle|NSNextFunctionKey|NSNEXTSTEPStringEncoding|NSNativeShortGlyphPacking|NSNaturalTextAlignment|NSNarrowFontMask|NSChangeReadOtherContents|NSChangeGrayCell|NSChangeGrayCellMask|NSChangeBackgroundCell|NSChangeBackgroundCellMask|NSChangeCleared|NSChangeDone|NSChangeUndone|NSChangeAutosaved|NSCMYKModeColorPanel|NSCMYKColorSpaceModel|NSCircularBezelStyle|NSCircularSlider|NSConstantValueExpressionType|NSContinuousCapacityLevelIndicatorStyle|NSContentsCellMask|NSContainsComparison|NSContainerSpecifierError|NSControlGlyph|NSControlKeyMask|NSCondensedFontMask|NSColorPanelRGBModeMask|NSColorPanelGrayModeMask|NSColorPanelHSBModeMask|NSColorPanelCMYKModeMask|NSColorPanelColorListModeMask|NSColorPanelCustomPaletteModeMask|NSColorPanelCrayonModeMask|NSColorPanelWheelModeMask|NSColorPanelAllModesMask|NSColorListModeColorPanel|NSCoreServiceDirectory|NSCompositeXOR|NSCompositeSourceIn|NSCompositeSourceOut|NSCompositeSourceOver|NSCompositeSourceAtop|NSCompositeHighlight|NSCompositeCopy|NSCompositeClear|NSCompositeDestinationIn|NSCompositeDestinationOut|NSCompositeDestinationOver|NSCompositeDestinationAtop|NSCompositePlusDarker|NSCompositePlusLighter|NSCompressedFontMask|NSCommandKeyMask|NSCustomSelectorPredicateOperatorType|NSCustomPaletteModeColorPanel|NSCursorUpdate|NSCursorUpdateMask|NSCursorPointingDevice|NSCurveToBezierPathElement|NSCenterTextAlignment|NSCenterTabStopType|NSCellHighlighted|NSCellHasImageHorizontal|NSCellHasImageOnLeftOrBottom|NSCellHasOverlappingImage|NSCellChangesContents|NSCellIsBordered|NSCellIsInsetButton|NSCellDisabled|NSCellEditable|NSCellLightsByGray|NSCellLightsByBackground|NSCellLightsByContents|NSCellAllowsMixedState|NSClipPagination|NSClosePathBezierPathElement|NSClosableWindowMask|NSClockAndCalendarDatePickerStyle|NSClearControlTint|NSClearDisplayFunctionKey|NSClearLineFunctionKey|NSCaseInsensitiveSearch|NSCaseInsensitivePredicateOption|NSCannotCreateScriptCommandError|NSCancelButton|NSCancelTextMovement|NSCachesDirectory|NSCalculationNoError|NSCalculationOverflow|NSCalculationDivideByZero|NSCalculationUnderflow|NSCalculationLossOfPrecision|NSCarriageReturnCharacter|NSCriticalRequest|NSCriticalAlertStyle|NSCrayonModeColorPanel|NSThickSquareBezelStyle|NSThickerSquareBezelStyle|NSTypesetterBehavior|NSTypesetterHorizontalTabAction|NSTypesetterContainerBreakAction|NSTypesetterZeroAdvancementAction|NSTypesetterOriginalBehavior|NSTypesetterParagraphBreakAction|NSTypesetterWhitespaceAction|NSTypesetterLineBreakAction|NSTypesetterLatestBehavior|NSTickMarkRight|NSTickMarkBelow|NSTickMarkLeft|NSTickMarkAbove|NSTitledWindowMask|NSTimeZoneDatePickerElementFlag|NSToolbarItemVisibilityPriorityStandard|NSToolbarItemVisibilityPriorityHigh|NSToolbarItemVisibilityPriorityUser|NSToolbarItemVisibilityPriorityLow|NSTopTabsBezelBorder|NSToggleButton|NSTIFFCompressionNone|NSTIFFCompressionNEXT|NSTIFFCompressionCCITTFAX3|NSTIFFCompressionCCITTFAX4|NSTIFFCompressionOldJPEG|NSTIFFCompressionJPEG|NSTIFFCompressionPackBits|NSTIFFCompressionLZW|NSTIFFFileType|NSTerminateNow|NSTerminateCancel|NSTerminateLater|NSTextReadInapplicableDocumentTypeError|NSTextReadWriteErrorMinimum|NSTextReadWriteErrorMaximum|NSTextBlockMinimumHeight|NSTextBlockMinimumWidth|NSTextBlockMiddleAlignment|NSTextBlockMargin|NSTextBlockMaximumHeight|NSTextBlockMaximumWidth|NSTextBlockBottomAlignment|NSTextBlockBorder|NSTextBlockBaselineAlignment|NSTextBlockHeight|NSTextBlockTopAlignment|NSTextBlockPercentageValueType|NSTextBlockPadding|NSTextBlockWidth|NSTextBlockAbsoluteValueType|NSTextStorageEditedCharacters|NSTextStorageEditedAttributes|NSTextCellType|NSTexturedRoundedBezelStyle|NSTexturedBackgroundWindowMask|NSTexturedSquareBezelStyle|NSTextTableFixedLayoutAlgorithm|NSTextTableAutomaticLayoutAlgorithm|NSTextFieldRoundedBezel|NSTextFieldSquareBezel|NSTextFieldAndStepperDatePickerStyle|NSTextWriteInapplicableDocumentTypeError|NSTextListPrependEnclosingMarker|NSTwoByteGlyphPacking|NSTabCharacter|NSTabTextMovement|NSTabletPoint|NSTabletPointMask|NSTabletPointEventSubtype|NSTabletProximity|NSTabletProximityMask|NSTabletProximityEventSubtype|NSTableColumnNoResizing|NSTableColumnUserResizingMask|NSTableColumnAutoresizingMask|NSTableViewReverseSequentialColumnAutoresizingStyle|NSTableViewGridNone|NSTableViewSolidHorizontalGridLineMask|NSTableViewSolidVerticalGridLineMask|NSTableViewSequentialColumnAutoresizingStyle|NSTableViewNoColumnAutoresizing|NSTableViewUniformColumnAutoresizingStyle|NSTableViewFirstColumnOnlyAutoresizingStyle|NSTableViewLastColumnOnlyAutoresizingStyle|NSTrackModeMatrix|NSInsertCharFunctionKey|NSInsertFunctionKey|NSInsertLineFunctionKey|NSIntType|NSInternalScriptError|NSInternalSpecifierError|NSIndexSubelement|NSInvalidIndexSpecifierError|NSInformationalRequest|NSInformationalAlertStyle|NSInPredicateOperatorType|NSItalicFontMask|NSISO2022JPStringEncoding|NSISOLatin1StringEncoding|NSISOLatin2StringEncoding|NSIdentityMappingCharacterCollection|NSIllegalTextMovement|NSImageRight|NSImageRepMatchesDevice|NSImageRepLoadStatusReadingHeader|NSImageRepLoadStatusCompleted|NSImageRepLoadStatusInvalidData|NSImageRepLoadStatusUnexpectedEOF|NSImageRepLoadStatusUnknownType|NSImageRepLoadStatusWillNeedAllData|NSImageBelow|NSImageCellType|NSImageCacheBySize|NSImageCacheNever|NSImageCacheDefault|NSImageCacheAlways|NSImageInterpolationHigh|NSImageInterpolationNone|NSImageInterpolationDefault|NSImageInterpolationLow|NSImageOnly|NSImageOverlaps|NSImageFrameGroove|NSImageFrameGrayBezel|NSImageFrameButton|NSImageFrameNone|NSImageFramePhoto|NSImageLoadStatusReadError|NSImageLoadStatusCompleted|NSImageLoadStatusCancelled|NSImageLoadStatusInvalidData|NSImageLoadStatusUnexpectedEOF|NSImageLeft|NSImageAlignRight|NSImageAlignBottom|NSImageAlignBottomRight|NSImageAlignBottomLeft|NSImageAlignCenter|NSImageAlignTop|NSImageAlignTopRight|NSImageAlignTopLeft|NSImageAlignLeft|NSImageAbove|NSOnState|NSOneByteGlyphPacking|NSOnOffButton|NSOnlyScrollerArrows|NSOtherMouseDown|NSOtherMouseDownMask|NSOtherMouseDragged|NSOtherMouseDraggedMask|NSOtherMouseUp|NSOtherMouseUpMask|NSOtherTextMovement|NSOSF1OperatingSystem|NSOpenGLGOResetLibrary|NSOpenGLGORetainRenderers|NSOpenGLGOClearFormatCache|NSOpenGLGOFormatCacheSize|NSOpenGLPFARobust|NSOpenGLPFARendererID|NSOpenGLPFAMinimumPolicy|NSOpenGLPFAMultisample|NSOpenGLPFAMultiScreen|NSOpenGLPFAMPSafe|NSOpenGLPFAMaximumPolicy|NSOpenGLPFABackingStore|NSOpenGLPFAScreenMask|NSOpenGLPFAStencilSize|NSOpenGLPFAStereo|NSOpenGLPFASingleRenderer|NSOpenGLPFASupersample|NSOpenGLPFASamples|NSOpenGLPFASampleBuffers|NSOpenGLPFASampleAlpha|NSOpenGLPFANoRecovery|NSOpenGLPFAColorSize|NSOpenGLPFAColorFloat|NSOpenGLPFACompliant|NSOpenGLPFAClosestPolicy|NSOpenGLPFAOffScreen|NSOpenGLPFADoubleBuffer|NSOpenGLPFADepthSize|NSOpenGLPFAPixelBuffer|NSOpenGLPFAVirtualScreenCount|NSOpenGLPFAFullScreen|NSOpenGLPFAWindow|NSOpenGLPFAAccumSize|NSOpenGLPFAAccelerated|NSOpenGLPFAAuxBuffers|NSOpenGLPFAAuxDepthStencil|NSOpenGLPFAAlphaSize|NSOpenGLPFAAllRenderers|NSOpenStepUnicodeReservedBase|NSOperationNotSupportedForKeyScriptError|NSOperationNotSupportedForKeySpecifierError|NSOffState|NSOKButton|NSOrPredicateType|NSObjCBitfield|NSObjCBoolType|NSObjCShortType|NSObjCStringType|NSObjCStructType|NSObjCSelectorType|NSObjCNoType|NSObjCCharType|NSObjCObjectType|NSObjCDoubleType|NSObjCUnionType|NSObjCPointerType|NSObjCVoidType|NSObjCFloatType|NSObjCLongType|NSObjCLonglongType|NSObjCArrayType|NSDisclosureBezelStyle|NSDiscreteCapacityLevelIndicatorStyle|NSDisplayWindowRunLoopOrdering|NSDiacriticInsensitivePredicateOption|NSDirectSelection|NSDirectPredicateModifier|NSDocModalWindowMask|NSDocumentDirectory|NSDocumentationDirectory|NSDoubleType|NSDownTextMovement|NSDownArrowFunctionKey|NSDescendingPageOrder|NSDesktopDirectory|NSDecimalTabStopType|NSDeviceNColorSpaceModel|NSDeviceIndependentModifierFlagsMask|NSDeveloperDirectory|NSDeveloperApplicationDirectory|NSDefaultControlTint|NSDefaultTokenStyle|NSDeleteCharacter|NSDeleteCharFunctionKey|NSDeleteFunctionKey|NSDeleteLineFunctionKey|NSDemoApplicationDirectory|NSDayCalendarUnit|NSDateFormatterMediumStyle|NSDateFormatterBehavior10|NSDateFormatterBehaviorDefault|NSDateFormatterShortStyle|NSDateFormatterNoStyle|NSDateFormatterFullStyle|NSDateFormatterLongStyle|NSDrawerClosingState|NSDrawerClosedState|NSDrawerOpeningState|NSDrawerOpenState|NSDragOperationGeneric|NSDragOperationMove|NSDragOperationNone|NSDragOperationCopy|NSDragOperationDelete|NSDragOperationPrivate|NSDragOperationEvery|NSDragOperationLink|NSDragOperationAll|NSUserCancelledError|NSUserDirectory|NSUserDomainMask|NSUserFunctionKey|NSURLHandleNotLoaded|NSURLHandleLoadSucceeded|NSURLHandleLoadInProgress|NSURLHandleLoadFailed|NSURLCredentialPersistenceNone|NSURLCredentialPersistencePermanent|NSURLCredentialPersistenceForSession|NSUnscaledWindowMask|NSUncachedRead|NSUnicodeStringEncoding|NSUnitalicFontMask|NSUnifiedTitleAndToolbarWindowMask|NSUndoCloseGroupingRunLoopOrdering|NSUndoFunctionKey|NSUndefinedDateComponent|NSUnderlineStyleSingle|NSUnderlineStyleNone|NSUnderlineStyleThick|NSUnderlineStyleDouble|NSUnderlinePatternSolid|NSUnderlinePatternDot|NSUnderlinePatternDash|NSUnderlinePatternDashDot|NSUnderlinePatternDashDotDot|NSUnknownColorSpaceModel|NSUnknownPointingDevice|NSUnknownPageOrder|NSUnknownKeyScriptError|NSUnknownKeySpecifierError|NSUnboldFontMask|NSUtilityWindowMask|NSUTF8StringEncoding|NSUpdateWindowsRunLoopOrdering|NSUpTextMovement|NSUpArrowFunctionKey|NSJustifiedTextAlignment|NSJPEG2000FileType|NSJPEGFileType|NSJapaneseEUCGlyphPacking|NSJapaneseEUCStringEncoding|NSPostNow|NSPosterFontMask|NSPostWhenIdle|NSPostASAP|NSPositionReplace|NSPositionBefore|NSPositionBeginning|NSPositionEnd|NSPositionAfter|NSPositiveIntType|NSPositiveDoubleType|NSPositiveFloatType|NSPopUpNoArrow|NSPopUpArrowAtBottom|NSPopUpArrowAtCenter|NSPowerOffEventType|NSPortraitOrientation|NSPNGFileType|NSPushInCell|NSPushInCellMask|NSPushOnPushOffButton|NSPenTipMask|NSPenUpperSideMask|NSPenPointingDevice|NSPenLowerSideMask|NSPeriodic|NSPeriodicMask|NSPPScaleField|NSPPStatusTitle|NSPPStatusField|NSPPSaveButton|NSPPNoteTitle|NSPPNoteField|NSPPNameTitle|NSPPNameField|NSPPCopiesField|NSPPTitleField|NSPPImageButton|NSPPOptionsButton|NSPPPaperFeedButton|NSPPPageRangeTo|NSPPPageRangeFrom|NSPPPageChoiceMatrix|NSPPPreviewButton|NSPPLayoutButton|NSPlainTextTokenStyle|NSPauseFunctionKey|NSParagraphSeparatorCharacter|NSPageDownFunctionKey|NSPageUpFunctionKey|NSPrintingReplyLater|NSPrintingSuccess|NSPrintingCancelled|NSPrintingFailure|NSPrintScreenFunctionKey|NSPrinterTableNotFound|NSPrinterTableOK|NSPrinterTableError|NSPrintFunctionKey|NSPropertyListXMLFormat|NSPropertyListMutableContainers|NSPropertyListMutableContainersAndLeaves|NSPropertyListBinaryFormat|NSPropertyListImmutable|NSPropertyListOpenStepFormat|NSProprietaryStringEncoding|NSProgressIndicatorBarStyle|NSProgressIndicatorSpinningStyle|NSProgressIndicatorPreferredSmallThickness|NSProgressIndicatorPreferredThickness|NSProgressIndicatorPreferredLargeThickness|NSProgressIndicatorPreferredAquaThickness|NSPressedTab|NSPrevFunctionKey|NSPLHeightForm|NSPLCancelButton|NSPLTitleField|NSPLImageButton|NSPLOKButton|NSPLOrientationMatrix|NSPLUnitsButton|NSPLPaperNameButton|NSPLWidthForm|NSEnterCharacter|NSEndsWithComparison|NSEndsWithPredicateOperatorType|NSEndFunctionKey|NSEvenOddWindingRule|NSEverySubelement|NSEvaluatedObjectExpressionType|NSEqualToComparison|NSEqualToPredicateOperatorType|NSEraserPointingDevice|NSEraCalendarUnit|NSEraDatePickerElementFlag|NSExclude10|NSExcludeQuickDrawElementsIconCreationOption|NSExpandedFontMask|NSExecuteFunctionKey|NSViewMinXMargin|NSViewMinYMargin|NSViewMaxXMargin|NSViewMaxYMargin|NSViewHeightSizable|NSViewNotSizable|NSViewWidthSizable|NSViaPanelFontAction|NSVerticalRuler|NSValidationErrorMinimum|NSValidationErrorMaximum|NSVariableExpressionType|NSKeySpecifierEvaluationScriptError|NSKeyDown|NSKeyDownMask|NSKeyUp|NSKeyUpMask|NSKeyPathExpressionType|NSKeyValueMinusSetMutation|NSKeyValueSetSetMutation|NSKeyValueChangeReplacement|NSKeyValueChangeRemoval|NSKeyValueChangeSetting|NSKeyValueChangeInsertion|NSKeyValueIntersectSetMutation|NSKeyValueObservingOptionNew|NSKeyValueObservingOptionOld|NSKeyValueUnionSetMutation|NSKeyValueValidationError|NSQTMovieNormalPlayback|NSQTMovieLoopingBackAndForthPlayback|NSQTMovieLoopingPlayback|NSF11FunctionKey|NSF17FunctionKey|NSF12FunctionKey|NSF18FunctionKey|NSF13FunctionKey|NSF19FunctionKey|NSF14FunctionKey|NSF15FunctionKey|NSF1FunctionKey|NSF10FunctionKey|NSF16FunctionKey|NSF7FunctionKey|NSFindPanelActionReplace|NSFindPanelActionReplaceAndFind|NSFindPanelActionReplaceAll|NSFindPanelActionReplaceAllInSelection|NSFindPanelActionShowFindPanel|NSFindPanelActionSetFindString|NSFindPanelActionSelectAll|NSFindPanelActionSelectAllInSelection|NSFindPanelActionNext|NSFindPanelActionPrevious|NSFindFunctionKey|NSFitPagination|NSFileReadNoSuchFileError|NSFileReadNoPermissionError|NSFileReadCorruptFileError|NSFileReadInvalidFileNameError|NSFileReadInapplicableStringEncodingError|NSFileReadUnsupportedSchemeError|NSFileReadUnknownError|NSFileHandlingPanelCancelButton|NSFileHandlingPanelOKButton|NSFileNoSuchFileError|NSFileErrorMinimum|NSFileErrorMaximum|NSFileWriteNoPermissionError|NSFileWriteInvalidFileNameError|NSFileWriteInapplicableStringEncodingError|NSFileWriteOutOfSpaceError|NSFileWriteUnsupportedSchemeError|NSFileWriteUnknownError|NSFileLockingError|NSFixedPitchFontMask|NSF21FunctionKey|NSF27FunctionKey|NSF22FunctionKey|NSF28FunctionKey|NSF23FunctionKey|NSF29FunctionKey|NSF24FunctionKey|NSF25FunctionKey|NSF2FunctionKey|NSF20FunctionKey|NSF26FunctionKey|NSFontMonoSpaceTrait|NSFontModernSerifsClass|NSFontBoldTrait|NSFontSymbolicClass|NSFontScriptsClass|NSFontSlabSerifsClass|NSFontSansSerifClass|NSFontCondensedTrait|NSFontCollectionApplicationOnlyMask|NSFontClarendonSerifsClass|NSFontTransitionalSerifsClass|NSFontIntegerAdvancementsRenderingMode|NSFontItalicTrait|NSFontOldStyleSerifsClass|NSFontOrnamentalsClass|NSFontDefaultRenderingMode|NSFontUnknownClass|NSFontUIOptimizedTrait|NSFontPanelShadowEffectModeMask|NSFontPanelStandardModesMask|NSFontPanelStrikethroughEffectModeMask|NSFontPanelSizeModeMask|NSFontPanelCollectionModeMask|NSFontPanelTextColorEffectModeMask|NSFontPanelDocumentColorEffectModeMask|NSFontPanelUnderlineEffectModeMask|NSFontPanelFaceModeMask|NSFontPanelAllModesMask|NSFontPanelAllEffectsModeMask|NSFontExpandedTrait|NSFontVerticalTrait|NSFontFamilyClassMask|NSFontFreeformSerifsClass|NSFontAntialiasedRenderingMode|NSFontAntialiasedIntegerAdvancementsRenderingMode|NSFocusRingBelow|NSFocusRingTypeNone|NSFocusRingTypeDefault|NSFocusRingTypeExterior|NSFocusRingOnly|NSFocusRingAbove|NSFourByteGlyphPacking|NSFormattingError|NSFormattingErrorMinimum|NSFormattingErrorMaximum|NSFormFeedCharacter|NSF8FunctionKey|NSFunctionExpressionType|NSFunctionKeyMask|NSF31FunctionKey|NSF32FunctionKey|NSF33FunctionKey|NSF34FunctionKey|NSF35FunctionKey|NSF3FunctionKey|NSF30FunctionKey|NSF9FunctionKey|NSF4FunctionKey|NSFPRevertButton|NSFPSizeTitle|NSFPSizeField|NSFPSetButton|NSFPCurrentField|NSFPPreviewButton|NSFPPreviewField|NSFloatingPointSamplesBitmapFormat|NSFloatType|NSFlagsChanged|NSFlagsChangedMask|NSFaxButton|NSF5FunctionKey|NSF6FunctionKey|NSWheelModeColorPanel|NSWindowsNTOperatingSystem|NSWindowsCP1251StringEncoding|NSWindowsCP1252StringEncoding|NSWindowsCP1253StringEncoding|NSWindowsCP1254StringEncoding|NSWindowsCP1250StringEncoding|NSWindows95InterfaceStyle|NSWindows95OperatingSystem|NSWindowMiniaturizeButton|NSWindowMovedEventType|NSWindowBelow|NSWindowCloseButton|NSWindowToolbarButton|NSWindowZoomButton|NSWindowOut|NSWindowDocumentIconButton|NSWindowExposedEventType|NSWindowAbove|NSWorkspaceLaunchNewInstance|NSWorkspaceLaunchInhibitingBackgroundOnly|NSWorkspaceLaunchDefault|NSWorkspaceLaunchPreferringClassic|NSWorkspaceLaunchWithoutActivation|NSWorkspaceLaunchWithoutAddingToRecents|NSWorkspaceLaunchAsync|NSWorkspaceLaunchAndHide|NSWorkspaceLaunchAndHideOthers|NSWorkspaceLaunchAndPrint|NSWorkspaceLaunchAllowingClassicStartup|NSWeekdayCalendarUnit|NSWeekdayOrdinalCalendarUnit|NSWeekCalendarUnit|NSWantsBidiLevels|NSWarningAlertStyle|NSWritingDirectionRightToLeft|NSWritingDirectionNatural|NSWritingDirectionLeftToRight|NSWrapCalendarComponents|NSListModeMatrix|NSLineMovesRight|NSLineMovesDown|NSLineMovesUp|NSLineMovesLeft|NSLineBorder|NSLineBreakByCharWrapping|NSLineBreakByClipping|NSLineBreakByTruncatingMiddle|NSLineBreakByTruncatingHead|NSLineBreakByTruncatingTail|NSLineBreakByWordWrapping|NSLineSeparatorCharacter|NSLineSweepRight|NSLineSweepDown|NSLineSweepUp|NSLineSweepLeft|NSLineToBezierPathElement|NSLineDoesntMove|NSLinearSlider|NSLiteralSearch|NSLikePredicateOperatorType|NSLighterFontAction|NSLibraryDirectory|NSLocalDomainMask|NSLessThanComparison|NSLessThanOrEqualToComparison|NSLessThanOrEqualToPredicateOperatorType|NSLessThanPredicateOperatorType|NSLeftMouseDown|NSLeftMouseDownMask|NSLeftMouseDragged|NSLeftMouseDraggedMask|NSLeftMouseUp|NSLeftMouseUpMask|NSLeftTextMovement|NSLeftTextAlignment|NSLeftTabsBezelBorder|NSLeftTabStopType|NSLeftArrowFunctionKey|NSLayoutRightToLeft|NSLayoutNotDone|NSLayoutCantFit|NSLayoutOutOfGlyphs|NSLayoutDone|NSLayoutLeftToRight|NSLandscapeOrientation|NSLABColorSpaceModel|NSAsciiWithDoubleByteEUCGlyphPacking|NSAscendingPageOrder|NSAnyType|NSAnyPredicateModifier|NSAnyEventMask|NSAnchoredSearch|NSAnimationBlocking|NSAnimationNonblocking|NSAnimationNonblockingThreaded|NSAnimationEffectDisappearingItemDefault|NSAnimationEffectPoof|NSAnimationEaseIn|NSAnimationEaseInOut|NSAnimationEaseOut|NSAnimationLinear|NSAndPredicateType|NSAtBottom|NSAttachmentCharacter|NSAtomicWrite|NSAtTop|NSASCIIStringEncoding|NSAdobeGB1CharacterCollection|NSAdobeCNS1CharacterCollection|NSAdobeJapan1CharacterCollection|NSAdobeJapan2CharacterCollection|NSAdobeKorea1CharacterCollection|NSAddTraitFontAction|NSAdminApplicationDirectory|NSAutosaveOperation|NSAutoPagination|NSApplicationSupportDirectory|NSApplicationDirectory|NSApplicationDefined|NSApplicationDefinedMask|NSApplicationDelegateReplySuccess|NSApplicationDelegateReplyCancel|NSApplicationDelegateReplyFailure|NSApplicationDeactivatedEventType|NSApplicationActivatedEventType|NSAppKitDefined|NSAppKitDefinedMask|NSAlternateKeyMask|NSAlphaShiftKeyMask|NSAlphaNonpremultipliedBitmapFormat|NSAlphaFirstBitmapFormat|NSAlertSecondButtonReturn|NSAlertThirdButtonReturn|NSAlertOtherReturn|NSAlertDefaultReturn|NSAlertErrorReturn|NSAlertFirstButtonReturn|NSAlertAlternateReturn|NSAllScrollerParts|NSAllDomainsMask|NSAllPredicateModifier|NSAllLibrariesDirectory|NSAllApplicationsDirectory|NSArgumentsWrongScriptError|NSArgumentEvaluationScriptError|NSAboveBottom|NSAboveTop|NSAWTEventType","support.constant.notification.cocoa.leopard":"NSMenuDidBeginTrackingNotification|NSViewDidUpdateTrackingAreasNotification","support.constant.notification.cocoa":"NSMenuDidRemoveItemNotification|NSMenuDidSendActionNotification|NSMenuDidChangeItemNotification|NSMenuDidEndTrackingNotification|NSMenuDidAddItemNotification|NSMenuWillSendActionNotification|NSSystemColorsDidChangeNotification|NSSplitViewDidResizeSubviewsNotification|NSSplitViewWillResizeSubviewsNotification|NSContextHelpModeDidDeactivateNotification|NSContextHelpModeDidActivateNotification|NSControlTintDidChangeNotification|NSControlTextDidBeginEditingNotification|NSControlTextDidChangeNotification|NSControlTextDidEndEditingNotification|NSColorPanelColorDidChangeNotification|NSColorListDidChangeNotification|NSComboBoxSelectionIsChangingNotification|NSComboBoxSelectionDidChangeNotification|NSComboBoxWillDismissNotification|NSComboBoxWillPopUpNotification|NSClassDescriptionNeededForClassNotification|NSToolbarDidRemoveItemNotification|NSToolbarWillAddItemNotification|NSTextStorageDidProcessEditingNotification|NSTextStorageWillProcessEditingNotification|NSTextDidBeginEditingNotification|NSTextDidChangeNotification|NSTextDidEndEditingNotification|NSTextViewDidChangeSelectionNotification|NSTextViewDidChangeTypingAttributesNotification|NSTextViewWillChangeNotifyingTextViewNotification|NSTableViewSelectionIsChangingNotification|NSTableViewSelectionDidChangeNotification|NSTableViewColumnDidResizeNotification|NSTableViewColumnDidMoveNotification|NSImageRepRegistryDidChangeNotification|NSOutlineViewSelectionIsChangingNotification|NSOutlineViewSelectionDidChangeNotification|NSOutlineViewColumnDidResizeNotification|NSOutlineViewColumnDidMoveNotification|NSOutlineViewItemDidCollapseNotification|NSOutlineViewItemDidExpandNotification|NSOutlineViewItemWillCollapseNotification|NSOutlineViewItemWillExpandNotification|NSDrawerDidCloseNotification|NSDrawerDidOpenNotification|NSDrawerWillCloseNotification|NSDrawerWillOpenNotification|NSPopUpButtonCellWillPopUpNotification|NSPopUpButtonWillPopUpNotification|NSViewGlobalFrameDidChangeNotification|NSViewBoundsDidChangeNotification|NSViewFocusDidChangeNotification|NSViewFrameDidChangeNotification|NSFontSetChangedNotification|NSWindowDidResizeNotification|NSWindowDidResignMainNotification|NSWindowDidResignKeyNotification|NSWindowDidMiniaturizeNotification|NSWindowDidMoveNotification|NSWindowDidBecomeMainNotification|NSWindowDidBecomeKeyNotification|NSWindowDidChangeScreenNotification|NSWindowDidChangeScreenProfileNotification|NSWindowDidDeminiaturizeNotification|NSWindowDidUpdateNotification|NSWindowDidEndSheetNotification|NSWindowDidExposeNotification|NSWindowWillMiniaturizeNotification|NSWindowWillMoveNotification|NSWindowWillBeginSheetNotification|NSWindowWillCloseNotification|NSWorkspaceSessionDidResignActiveNotification|NSWorkspaceSessionDidBecomeActiveNotification|NSWorkspaceDidMountNotification|NSWorkspaceDidTerminateApplicationNotification|NSWorkspaceDidUnmountNotification|NSWorkspaceDidPerformFileOperationNotification|NSWorkspaceDidWakeNotification|NSWorkspaceDidLaunchApplicationNotification|NSWorkspaceWillSleepNotification|NSWorkspaceWillUnmountNotification|NSWorkspaceWillPowerOffNotification|NSWorkspaceWillLaunchApplicationNotification|NSAntialiasThresholdChangedNotification|NSApplicationDidResignActiveNotification|NSApplicationDidBecomeActiveNotification|NSApplicationDidHideNotification|NSApplicationDidChangeScreenParametersNotification|NSApplicationDidUnhideNotification|NSApplicationDidUpdateNotification|NSApplicationDidFinishLaunchingNotification|NSApplicationWillResignActiveNotification|NSApplicationWillBecomeActiveNotification|NSApplicationWillHideNotification|NSApplicationWillTerminateNotification|NSApplicationWillUnhideNotification|NSApplicationWillUpdateNotification|NSApplicationWillFinishLaunchingNotification|NSAppleEventManagerWillProcessFirstEventNotification","support.constant.cocoa.leopard":"NSRuleEditorRowTypeSimple|NSRuleEditorRowTypeCompound|NSRuleEditorNestingModeSingle|NSRuleEditorNestingModeSimple|NSRuleEditorNestingModeCompound|NSRuleEditorNestingModeList|NSGradientDrawsBeforeStartingLocation|NSGradientDrawsAfterEndingLocation|NSMinusSetExpressionType|NSMachPortDeallocateReceiveRight|NSMachPortDeallocateSendRight|NSMachPortDeallocateNone|NSMapTableStrongMemory|NSMapTableCopyIn|NSMapTableZeroingWeakMemory|NSMapTableObjectPointerPersonality|NSBoxCustom|NSBundleExecutableArchitectureX86|NSBundleExecutableArchitectureI386|NSBundleExecutableArchitecturePPC|NSBundleExecutableArchitecturePPC64|NSBetweenPredicateOperatorType|NSBackgroundStyleRaised|NSBackgroundStyleDark|NSBackgroundStyleLight|NSBackgroundStyleLowered|NSStringDrawingTruncatesLastVisibleLine|NSStringEncodingConversionExternalRepresentation|NSStringEncodingConversionAllowLossy|NSSubqueryExpressionType|NSSpeechSentenceBoundary|NSSpeechImmediateBoundary|NSSpeechWordBoundary|NSSpellingStateGrammarFlag|NSSpellingStateSpellingFlag|NSSplitViewDividerStyleThin|NSSplitViewDividerStyleThick|NSServiceRequestTimedOutError|NSServiceMiscellaneousError|NSServiceMalformedServiceDictionaryError|NSServiceInvalidPasteboardDataError|NSServiceErrorMinimum|NSServiceErrorMaximum|NSServiceApplicationNotFoundError|NSServiceApplicationLaunchFailedError|NSSegmentStyleRoundRect|NSSegmentStyleRounded|NSSegmentStyleSmallSquare|NSSegmentStyleCapsule|NSSegmentStyleTexturedRounded|NSSegmentStyleTexturedSquare|NSSegmentStyleAutomatic|NSHUDWindowMask|NSHashTableStrongMemory|NSHashTableCopyIn|NSHashTableZeroingWeakMemory|NSHashTableObjectPointerPersonality|NSNoModeColorPanel|NSNetServiceNoAutoRename|NSChangeRedone|NSContainsPredicateOperatorType|NSColorRenderingIntentRelativeColorimetric|NSColorRenderingIntentSaturation|NSColorRenderingIntentDefault|NSColorRenderingIntentPerceptual|NSColorRenderingIntentAbsoluteColorimetric|NSCollectorDisabledOption|NSCellHitNone|NSCellHitContentArea|NSCellHitTrackableArea|NSCellHitEditableTextArea|NSTimeZoneNameStyleShortStandard|NSTimeZoneNameStyleShortDaylightSaving|NSTimeZoneNameStyleStandard|NSTimeZoneNameStyleDaylightSaving|NSTextFieldDatePickerStyle|NSTableViewSelectionHighlightStyleRegular|NSTableViewSelectionHighlightStyleSourceList|NSTrackingMouseMoved|NSTrackingMouseEnteredAndExited|NSTrackingCursorUpdate|NSTrackingInVisibleRect|NSTrackingEnabledDuringMouseDrag|NSTrackingAssumeInside|NSTrackingActiveInKeyWindow|NSTrackingActiveInActiveApp|NSTrackingActiveWhenFirstResponder|NSTrackingActiveAlways|NSIntersectSetExpressionType|NSIndexedColorSpaceModel|NSImageScaleNone|NSImageScaleProportionallyDown|NSImageScaleProportionallyUpOrDown|NSImageScaleAxesIndependently|NSOpenGLPFAAllowOfflineRenderers|NSOperationQueueDefaultMaxConcurrentOperationCount|NSOperationQueuePriorityHigh|NSOperationQueuePriorityNormal|NSOperationQueuePriorityVeryHigh|NSOperationQueuePriorityVeryLow|NSOperationQueuePriorityLow|NSDiacriticInsensitiveSearch|NSDownloadsDirectory|NSUnionSetExpressionType|NSUTF16BigEndianStringEncoding|NSUTF16StringEncoding|NSUTF16LittleEndianStringEncoding|NSUTF32BigEndianStringEncoding|NSUTF32StringEncoding|NSUTF32LittleEndianStringEncoding|NSPointerFunctionsMachVirtualMemory|NSPointerFunctionsMallocMemory|NSPointerFunctionsStrongMemory|NSPointerFunctionsStructPersonality|NSPointerFunctionsCStringPersonality|NSPointerFunctionsCopyIn|NSPointerFunctionsIntegerPersonality|NSPointerFunctionsZeroingWeakMemory|NSPointerFunctionsOpaqueMemory|NSPointerFunctionsOpaquePersonality|NSPointerFunctionsObjectPointerPersonality|NSPointerFunctionsObjectPersonality|NSPathStyleStandard|NSPathStyleNavigationBar|NSPathStylePopUp|NSPatternColorSpaceModel|NSPrintPanelShowsScaling|NSPrintPanelShowsCopies|NSPrintPanelShowsOrientation|NSPrintPanelShowsPaperSize|NSPrintPanelShowsPageRange|NSPrintPanelShowsPageSetupAccessory|NSPrintPanelShowsPreview|NSExecutableRuntimeMismatchError|NSExecutableNotLoadableError|NSExecutableErrorMinimum|NSExecutableErrorMaximum|NSExecutableLinkError|NSExecutableLoadError|NSExecutableArchitectureMismatchError|NSKeyValueObservingOptionInitial|NSKeyValueObservingOptionPrior|NSFindPanelSubstringMatchTypeStartsWith|NSFindPanelSubstringMatchTypeContains|NSFindPanelSubstringMatchTypeEndsWith|NSFindPanelSubstringMatchTypeFullWord|NSFileReadTooLargeError|NSFileReadUnknownStringEncodingError|NSForcedOrderingSearch|NSWindowBackingLocationMainMemory|NSWindowBackingLocationDefault|NSWindowBackingLocationVideoMemory|NSWindowSharingReadOnly|NSWindowSharingReadWrite|NSWindowSharingNone|NSWindowCollectionBehaviorMoveToActiveSpace|NSWindowCollectionBehaviorCanJoinAllSpaces|NSWindowCollectionBehaviorDefault|NSWidthInsensitiveSearch|NSAggregateExpressionType"},t="\\\\(?:[abefnrtv'\"?\\\\]|[0-3]\\d{1,2}|[4-7]\\d?|222|x[a-zA-Z0-9]+)",n=[{regex:"\\b_cmd\\b",token:"variable.other.selector.objc"},{regex:"\\b(?:self|super)\\b",token:"variable.language.objc"}],r=new s(e),o=r.getRules();this.$keywordList=r.$keywordList,this.$rules={start:[{token:"comment",regex:"\\/\\/.*$"},i.getStartRule("doc-start"),{token:"comment",regex:"\\/\\*",next:"comment"},{token:["storage.type.objc","punctuation.definition.storage.type.objc","entity.name.type.objc","text","entity.other.inherited-class.objc"],regex:"(@)(interface|protocol)(?!.+;)(\\s+[A-Za-z_][A-Za-z0-9_]*)(\\s*:\\s*)([A-Za-z]+)"},{token:["storage.type.objc"],regex:"(@end)"},{token:["storage.type.objc","entity.name.type.objc","entity.other.inherited-class.objc"],regex:"(@implementation)(\\s+[A-Za-z_][A-Za-z0-9_]*)(\\s*?::\\s*(?:[A-Za-z][A-Za-z0-9]*))?"},{token:"string.begin.objc",regex:'@"',next:"constant_NSString"},{token:"storage.type.objc",regex:"\\bid\\s*<",next:"protocol_list"},{token:"keyword.control.macro.objc",regex:"\\bNS_DURING|NS_HANDLER|NS_ENDHANDLER\\b"},{token:["punctuation.definition.keyword.objc","keyword.control.exception.objc"],regex:"(@)(try|catch|finally|throw)\\b"},{token:["punctuation.definition.keyword.objc","keyword.other.objc"],regex:"(@)(defs|encode)\\b"},{token:["storage.type.id.objc","text"],regex:"(\\bid\\b)(\\s|\\n)?"},{token:"storage.type.objc",regex:"\\bIBOutlet|IBAction|BOOL|SEL|id|unichar|IMP|Class\\b"},{token:["punctuation.definition.storage.type.objc","storage.type.objc"],regex:"(@)(class|protocol)\\b"},{token:["punctuation.definition.storage.type.objc","punctuation"],regex:"(@selector)(\\s*\\()",next:"selectors"},{token:["punctuation.definition.storage.modifier.objc","storage.modifier.objc"],regex:"(@)(synchronized|public|private|protected|package)\\b"},{token:"constant.language.objc",regex:"\\bYES|NO|Nil|nil\\b"},{token:"support.variable.foundation",regex:"\\bNSApp\\b"},{token:r.getKeywords(),regex:"[a-zA-Z_$][a-zA-Z0-9_$]*\\b"},{token:"punctuation.section.scope.begin.objc",regex:"\\[",next:"bracketed_content"},{token:"meta.function.objc",regex:"^(?:-|\\+)\\s*"}],constant_NSString:[{token:"constant.character.escape.objc",regex:t},{token:"invalid.illegal.unknown-escape.objc",regex:"\\\\."},{token:"string",regex:'[^"\\\\]+'},{token:"punctuation.definition.string.end",regex:'"',next:"start"}],protocol_list:[{token:"punctuation.section.scope.end.objc",regex:">",next:"start"},{token:"support.other.protocol.objc",regex:"\bNS(?:GlyphStorage|M(?:utableCopying|enuItem)|C(?:hangeSpelling|o(?:ding|pying|lorPicking(?:Custom|Default)))|T(?:oolbarItemValidations|ext(?:Input|AttachmentCell))|I(?:nputServ(?:iceProvider|erMouseTracker)|gnoreMisspelledWords)|Obj(?:CTypeSerializationCallBack|ect)|D(?:ecimalNumberBehaviors|raggingInfo)|U(?:serInterfaceValidations|RL(?:HandleClient|DownloadDelegate|ProtocolClient|AuthenticationChallengeSender))|Validated(?:ToobarItem|UserInterfaceItem)|Locking)\b"}],selectors:[{token:"support.function.any-method.name-of-parameter.objc",regex:"\\b(?:[a-zA-Z_:][\\w]*)+"},{token:"punctuation",regex:"\\)",next:"start"}],bracketed_content:[{token:"punctuation.section.scope.end.objc",regex:"]",next:"start"},{token:["support.function.any-method.objc"],regex:"(?:predicateWithFormat:| NSPredicate predicateWithFormat:)",next:"start"},{token:"support.function.any-method.objc",regex:"\\w+(?::|(?=]))",next:"start"}],bracketed_strings:[{token:"punctuation.section.scope.end.objc",regex:"]",next:"start"},{token:"keyword.operator.logical.predicate.cocoa",regex:"\\b(?:AND|OR|NOT|IN)\\b"},{token:["invalid.illegal.unknown-method.objc","punctuation.separator.arguments.objc"],regex:"\\b(\\w+)(:)"},{regex:"\\b(?:ALL|ANY|SOME|NONE)\\b",token:"constant.language.predicate.cocoa"},{regex:"\\b(?:NULL|NIL|SELF|TRUE|YES|FALSE|NO|FIRST|LAST|SIZE)\\b",token:"constant.language.predicate.cocoa"},{regex:"\\b(?:MATCHES|CONTAINS|BEGINSWITH|ENDSWITH|BETWEEN)\\b",token:"keyword.operator.comparison.predicate.cocoa"},{regex:"\\bC(?:ASEINSENSITIVE|I)\\b",token:"keyword.other.modifier.predicate.cocoa"},{regex:"\\b(?:ANYKEY|SUBQUERY|CAST|TRUEPREDICATE|FALSEPREDICATE)\\b",token:"keyword.other.predicate.cocoa"},{regex:t,token:"constant.character.escape.objc"},{regex:"\\\\.",token:"invalid.illegal.unknown-escape.objc"},{token:"string",regex:'[^"\\\\]'},{token:"punctuation.definition.string.end.objc",regex:'"',next:"predicates"}],comment:[{token:"comment",regex:".*?\\*\\/",next:"start"},{defaultToken:"comment"}],methods:[{token:"meta.function.objc",regex:"(?=\\{|#)|;",next:"start"}]};for(var u in o)this.$rules[u]?this.$rules[u].push&&this.$rules[u].push.apply(this.$rules[u],o[u]):this.$rules[u]=o[u];this.$rules.bracketed_content=this.$rules.bracketed_content.concat(this.$rules.start,n),this.embedRules(i,"doc-",[i.getEndRule("start")])};r.inherits(o,s),t.ObjectiveCHighlightRules=o}),define("ace/mode/folding/cstyle",["require","exports","module","ace/lib/oop","ace/range","ace/mode/folding/fold_mode"],function(e,t,n){"use strict";var r=e("../../lib/oop"),i=e("../../range").Range,s=e("./fold_mode").FoldMode,o=t.FoldMode=function(e){e&&(this.foldingStartMarker=new RegExp(this.foldingStartMarker.source.replace(/\|[^|]*?$/,"|"+e.start)),this.foldingStopMarker=new RegExp(this.foldingStopMarker.source.replace(/\|[^|]*?$/,"|"+e.end)))};r.inherits(o,s),function(){this.foldingStartMarker=/([\{\[\(])[^\}\]\)]*$|^\s*(\/\*)/,this.foldingStopMarker=/^[^\[\{\(]*([\}\]\)])|^[\s\*]*(\*\/)/,this.singleLineBlockCommentRe=/^\s*(\/\*).*\*\/\s*$/,this.tripleStarBlockCommentRe=/^\s*(\/\*\*\*).*\*\/\s*$/,this.startRegionRe=/^\s*(\/\*|\/\/)#?region\b/,this._getFoldWidgetBase=this.getFoldWidget,this.getFoldWidget=function(e,t,n){var r=e.getLine(n);if(this.singleLineBlockCommentRe.test(r)&&!this.startRegionRe.test(r)&&!this.tripleStarBlockCommentRe.test(r))return"";var i=this._getFoldWidgetBase(e,t,n);return!i&&this.startRegionRe.test(r)?"start":i},this.getFoldWidgetRange=function(e,t,n,r){var i=e.getLine(n);if(this.startRegionRe.test(i))return this.getCommentRegionBlock(e,i,n);var s=i.match(this.foldingStartMarker);if(s){var o=s.index;if(s[1])return this.openingBracketBlock(e,s[1],n,o);var u=e.getCommentFoldRange(n,o+s[0].length,1);return u&&!u.isMultiLine()&&(r?u=this.getSectionRange(e,n):t!="all"&&(u=null)),u}if(t==="markbegin")return;var s=i.match(this.foldingStopMarker);if(s){var o=s.index+s[0].length;return s[1]?this.closingBracketBlock(e,s[1],n,o):e.getCommentFoldRange(n,o,-1)}},this.getSectionRange=function(e,t){var n=e.getLine(t),r=n.search(/\S/),s=t,o=n.length;t+=1;var u=t,a=e.getLength();while(++t<a){n=e.getLine(t);var f=n.search(/\S/);if(f===-1)continue;if(r>f)break;var l=this.getFoldWidgetRange(e,"all",t);if(l){if(l.start.row<=s)break;if(l.isMultiLine())t=l.end.row;else if(r==f)break}u=t}return new i(s,o,u,e.getLine(u).length)},this.getCommentRegionBlock=function(e,t,n){var r=t.search(/\s*$/),s=e.getLength(),o=n,u=/^\s*(?:\/\*|\/\/|--)#?(end)?region\b/,a=1;while(++n<s){t=e.getLine(n);var f=u.exec(t);if(!f)continue;f[1]?a--:a++;if(!a)break}var l=n;if(l>o)return new i(o,r,l,t.length)}}.call(o.prototype)}),define("ace/mode/objectivec",["require","exports","module","ace/lib/oop","ace/mode/text","ace/mode/objectivec_highlight_rules","ace/mode/folding/cstyle"],function(e,t,n){"use strict";var r=e("../lib/oop"),i=e("./text").Mode,s=e("./objectivec_highlight_rules").ObjectiveCHighlightRules,o=e("./folding/cstyle").FoldMode,u=function(){this.HighlightRules=s,this.foldingRules=new o,this.$behaviour=this.$defaultBehaviour};r.inherits(u,i),function(){this.lineCommentStart="//",this.blockComment={start:"/*",end:"*/"},this.$id="ace/mode/objectivec"}.call(u.prototype),t.Mode=u});                (function() {
                    window.require(["ace/mode/objectivec"], function(m) {
                        if (typeof module == "object" && typeof exports == "object" && module) {
                            module.exports = m;
                        }
                    });
                })();
            