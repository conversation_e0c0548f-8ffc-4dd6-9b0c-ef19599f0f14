define("ace/ext/hardwrap",["require","exports","module","ace/range","ace/editor","ace/config"],function(e,t,n){"use strict";function i(e,t){function m(e,t,n){if(e.length<t)return;var r=e.slice(0,t),i=e.slice(t),s=/^(?:(\s+)|(\S+)(\s+))/.exec(i),o=/(?:(\s+)|(\s+)(\S+))$/.exec(r),u=0,a=0;o&&!o[2]&&(u=t-o[1].length,a=t),s&&!s[2]&&(u||(u=t),a=t+s[1].length);if(u)return{start:u,end:a};if(o&&o[2]&&o.index>n)return{start:o.index,end:o.index+o[2].length};if(s&&s[2])return u=t+s[2].length,{start:u,end:u+s[3].length}}var n=t.column||e.getOption("printMarginColumn"),i=t.allowMerge!=0,s=Math.min(t.startRow,t.endRow),o=Math.max(t.startRow,t.endRow),u=e.session;while(s<=o){var a=u.getLine(s);if(a.length>n){var f=m(a,n,5);if(f){var l=/^\s*/.exec(a)[0];u.replace(new r(s,f.start,s,f.end),"\n"+l)}o++}else if(i&&/\S/.test(a)&&s!=o){var c=u.getLine(s+1);if(c&&/\S/.test(c)){var h=a.replace(/\s+$/,""),p=c.replace(/^\s+/,""),d=h+" "+p,f=m(d,n,5);if(f&&f.start>h.length||d.length<n){var v=new r(s,h.length,s+1,c.length-p.length);u.replace(v," "),s--,o--}else h.length<a.length&&u.remove(new r(s,h.length,s,a.length))}}s++}}function s(e){if(e.command.name=="insertstring"&&/\S/.test(e.args)){var t=e.editor,n=t.selection.cursor;if(n.column<=t.renderer.$printMarginColumn)return;var r=t.session.$undoManager.$lastDelta;i(t,{startRow:n.row,endRow:n.row,allowMerge:!1}),r!=t.session.$undoManager.$lastDelta&&t.session.markUndoGroup()}}var r=e("../range").Range,o=e("../editor").Editor;e("../config").defineOptions(o.prototype,"editor",{hardWrap:{set:function(e){e?this.commands.on("afterExec",s):this.commands.off("afterExec",s)},value:!1}}),t.hardWrap=i}),define("ace/keyboard/vim",["require","exports","module","ace/range","ace/lib/event_emitter","ace/lib/dom","ace/lib/oop","ace/lib/keys","ace/lib/event","ace/search","ace/lib/useragent","ace/search_highlight","ace/commands/multi_select_commands","ace/mode/text","ace/ext/hardwrap","ace/multi_select"],function(e,t,n){"use strict";function r(){function t(e){return typeof e!="object"?e+"":"line"in e?e.line+":"+e.ch:"anchor"in e?t(e.anchor)+"->"+t(e.head):Array.isArray(e)?"["+e.map(function(e){return t(e)})+"]":JSON.stringify(e)}var e="";for(var n=0;n<arguments.length;n++){var r=arguments[n],i=t(r);e+=i+"  "}console.log(e)}function g(e){return{row:e.line,column:e.ch}}function y(e){return new w(e.row,e.column)}function E(e,t,n){if(t.line===n.line&&t.ch>=n.ch-1){var r=e.getLine(t.line),i=r.charCodeAt(t.ch);55296<=i&&i<=55551&&(n.ch+=1)}return{start:t,end:n}}function C(e){e.setOption("disableInput",!0),e.setOption("showCursorWhenSelecting",!1),m.signal(e,"vim-mode-change",{mode:"normal"}),e.on("cursorActivity",cr),Y(e),m.on(e.getInputField(),"paste",L(e))}function k(e){e.setOption("disableInput",!1),e.off("cursorActivity",cr),m.off(e.getInputField(),"paste",L(e)),e.state.vim=null,Wn&&clearTimeout(Wn)}function L(e){var t=e.state.vim;return t.onPasteFn||(t.onPasteFn=function(){t.insertMode||(e.setCursor(Mt(e.getCursor(),0,1)),kt.enterInsertMode(e,{},t))}),t.onPasteFn}function j(e,t){return t>=e.firstLine()&&t<=e.lastLine()}function F(e){return/^[a-z]$/.test(e)}function I(e){return"()[]{}".indexOf(e)!=-1}function q(e){return A.test(e)}function R(e){return H.test(e)}function U(e){return/^\s*$/.test(e)}function z(e){return".?!".indexOf(e)!=-1}function W(e,t){for(var n=0;n<t.length;n++)if(t[n]==e)return!0;return!1}function V(e,t,n,r,i){if(t===undefined&&!i)throw Error("defaultValue is required unless callback is provided");n||(n="string"),X[e]={type:n,defaultValue:t,callback:i};if(r)for(var s=0;s<r.length;s++)X[r[s]]=X[e];t&&$(e,t)}function $(e,t,n,r){var i=X[e];r=r||{};var s=r.scope;if(!i)return new Error("Unknown option: "+e);if(i.type=="boolean"){if(t&&t!==!0)return new Error("Invalid argument: "+e+"="+t);t!==!1&&(t=!0)}i.callback?(s!=="local"&&i.callback(t,undefined),s!=="global"&&n&&i.callback(t,n)):(s!=="local"&&(i.value=i.type=="boolean"?!!t:t),s!=="global"&&n&&(n.state.vim.options[e]={value:t}))}function J(e,t,n){var r=X[e];n=n||{};var i=n.scope;if(!r)return new Error("Unknown option: "+e);if(r.callback){var s=t&&r.callback(undefined,t);if(i!=="global"&&s!==undefined)return s;if(i!=="local")return r.callback();return}var s=i!=="global"&&t&&t.state.vim.options[e];return(s||i!=="local"&&r||{}).value}function G(){this.latestRegister=undefined,this.isPlaying=!1,this.isRecording=!1,this.replaySearchQueries=[],this.onRecordingDone=undefined,this.lastInsertModeChanges=Q()}function Y(e){return e.state.vim||(e.state.vim={inputState:new dt,lastEditInputState:undefined,lastEditActionCommand:undefined,lastHPos:-1,lastHSPos:-1,lastMotion:null,marks:{},insertMode:!1,insertModeReturn:!1,insertModeRepeat:undefined,visualMode:!1,visualLine:!1,visualBlock:!1,lastSelection:null,lastPastedText:null,sel:{},options:{},expectLiteralNext:!1}),e.state.vim}function et(){Z={searchQuery:null,searchIsReversed:!1,lastSubstituteReplacePart:undefined,jumpList:K(),macroModeState:new G,lastCharacterSearch:{increment:0,forward:!0,selectedCharacter:""},registerController:new bt({}),searchHistoryController:new wt,exCommandHistoryController:new wt};for(var e in X){var t=X[e];t.value=t.defaultValue}}function ot(e){function o(e){typeof e=="string"?st.value=e:st=null}if(e[0]=="<"){var t=e.toLowerCase().slice(1,-1),n=t.split("-");t=n.pop()||"";if(t=="lt")e="<";else if(t=="space")e=" ";else if(t=="cr")e="\n";else if(lt[t]){var r=st.value,i={key:lt[t],target:{value:r,selectionEnd:r.length,selectionStart:r.length}};st.onKeyDown&&st.onKeyDown(i,st.value,o),st&&st.onKeyUp&&st.onKeyUp(i,st.value,o);return}}if(e=="\n"){var s=st;st=null,s.onClose&&s.onClose(s.value)}else st.value=(st.value||"")+e}function ut(e,t,n){var r=it;if(n){if(rt.indexOf(n)!=-1)return;rt.push(n),it=n.noremap!=0}try{var i=Y(e),s=/<(?:[CSMA]-)*\w+>|./gi,o;while(o=s.exec(t)){var u=o[0],a=i.insertMode;if(st){ot(u);continue}var f=nt.handleKey(e,u,"mapping");if(!f&&a&&i.insertMode){if(u[0]=="<"){var l=u.toLowerCase().slice(1,-1),c=l.split("-");l=c.pop()||"";if(l=="lt")u="<";else if(l=="space")u=" ";else if(l=="cr")u="\n";else{if(lt.hasOwnProperty(l)){u=lt[l],mr(e,u);continue}u=u[0],s.lastIndex=o.index+1}}e.replaceSelection(u)}}}finally{rt.pop(),it=rt.length?r:!1;if(!rt.length&&st){var h=st;st=null,qn(e,h)}}}function ct(e,t){var n=e.key;if(ft[n])return;n.length>1&&n[0]=="n"&&(n=n.replace("Numpad","")),n=at[n]||n;var r="";e.ctrlKey&&(r+="C-"),e.altKey&&(r+="A-"),e.metaKey&&(r+="M-"),m.isMac&&e.altKey&&!e.metaKey&&!e.ctrlKey&&(r=r.slice(2)),(r||n.length>1)&&e.shiftKey&&(r+="S-");if(t&&!t.expectLiteralNext&&n.length==1)if(N.keymap&&n in N.keymap){if(N.remapCtrl!=0||!r)n=N.keymap[n]}else if(n.charCodeAt(0)>255){var i=e.code&&e.code.slice(-1)||"";e.shiftKey||(i=i.toLowerCase()),i&&(n=i)}return r+=n,r.length>1&&(r="<"+r+">"),r}function ht(e,t){N.string!==e&&(N=pt(e)),N.remapCtrl=t}function pt(e){function n(e){return e.split(/\\?(.)/).filter(Boolean)}var t={};return e?(e.split(/((?:[^\\,]|\\.)+),/).map(function(e){if(!e)return;var r=e.split(/((?:[^\\;]|\\.)+);/);if(r.length==3){var i=n(r[1]),s=n(r[2]);if(i.length!==s.length)return;for(var o=0;o<i.length;++o)t[i[o]]=s[o]}else if(r.length==1){var u=n(e);if(u.length%2!==0)return;for(var o=0;o<u.length;o+=2)t[u[o]]=u[o+1]}}),{keymap:t,string:e}):{keymap:t,string:""}}function dt(){this.prefixRepeat=[],this.motionRepeat=[],this.operator=null,this.operatorArgs=null,this.motion=null,this.motionArgs=null,this.keyBuffer=[],this.registerName=null,this.changeQueue=null}function vt(e,t){e.state.vim.inputState=new dt,e.state.vim.expectLiteralNext=!1,m.signal(e,"vim-command-done",t)}function mt(){this.removed=[],this.inserted=""}function gt(e,t,n){this.clear(),this.keyBuffer=[e||""],this.insertModeChanges=[],this.searchQueries=[],this.linewise=!!t,this.blockwise=!!n}function yt(e,t){var n=Z.registerController.registers;if(!e||e.length!=1)throw Error("Register name must be 1 character");n[e]=t,D.push(e)}function bt(e){this.registers=e,this.unnamedRegister=e['"']=new gt,e["."]=new gt,e[":"]=new gt,e["/"]=new gt,e["+"]=new gt}function wt(){this.historyBuffer=[],this.iterator=0,this.initialPrefix=null}function xt(e,t){St[e]=t}function Tt(e,t){var n=[];for(var r=0;r<t;r++)n.push(e);return n}function Ct(e,t){Nt[e]=t}function Lt(e,t){kt[e]=t}function At(e,t,n){var r=e.state.vim,i=r.insertMode||r.visualMode,s=Math.min(Math.max(e.firstLine(),t.line),e.lastLine()),o=e.getLine(s),u=o.length-1+Number(!!i),a=Math.min(Math.max(0,t.ch),u),f=o.charCodeAt(a);if(56320<=f&&f<=57343){var l=1;n&&n.line==s&&n.ch>a&&(l=-1),a+=l,a>u&&(a-=2)}return new w(s,a)}function Ot(e){var t={};for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n]);return t}function Mt(e,t,n){return typeof t=="object"&&(n=t.ch,t=t.line),new w(e.line+t,e.ch+n)}function _t(e,t,n,r){r.operator&&(n="operatorPending");var i,s=[],o=[],u=it?t.length-x:0;for(var a=u;a<t.length;a++){var f=t[a];if(n=="insert"&&f.context!="insert"||f.context&&f.context!=n||r.operator&&f.type=="action"||!(i=Dt(e,f.keys)))continue;i=="partial"&&s.push(f),i=="full"&&o.push(f)}return{partial:s.length&&s,full:o.length&&o}}function Dt(e,t){var n=t.slice(-11)=="<character>",r=t.slice(-10)=="<register>";if(n||r){var i=t.length-(n?11:10),s=e.slice(0,i),o=t.slice(0,i);return s==o&&e.length>i?"full":o.indexOf(s)==0?"partial":!1}return e==t?"full":t.indexOf(e)==0?"partial":!1}function Pt(e){var t=/^.*(<[^>]+>)$/.exec(e),n=t?t[1]:e.slice(-1);if(n.length>1)switch(n){case"<CR>":n="\n";break;case"<Space>":n=" ";break;default:n=""}return n}function Ht(e,t,n){return function(){for(var r=0;r<n;r++)t(e)}}function Bt(e){return new w(e.line,e.ch)}function jt(e,t){return e.ch==t.ch&&e.line==t.line}function Ft(e,t){return e.line<t.line?!0:e.line==t.line&&e.ch<t.ch?!0:!1}function It(e,t){return arguments.length>2&&(t=It.apply(undefined,Array.prototype.slice.call(arguments,1))),Ft(e,t)?e:t}function qt(e,t){return arguments.length>2&&(t=qt.apply(undefined,Array.prototype.slice.call(arguments,1))),Ft(e,t)?t:e}function Rt(e,t,n){var r=Ft(e,t),i=Ft(t,n);return r&&i}function Ut(e,t){return e.getLine(t).length}function zt(e){return e.trim?e.trim():e.replace(/^\s+|\s+$/g,"")}function Wt(e){return e.replace(/([.?*+$\[\]\/\\(){}|\-])/g,"\\$1")}function Xt(e,t,n){var r=Ut(e,t),i=(new Array(n-r+1)).join(" ");e.setCursor(new w(t,r)),e.replaceRange(i,e.getCursor())}function Vt(e,t){var n=[],r=e.listSelections(),i=Bt(e.clipPos(t)),s=!jt(t,i),o=e.getCursor("head"),u=Jt(r,o),a=jt(r[u].head,r[u].anchor),f=r.length-1,l=f-u>u?f:0,c=r[l].anchor,h=Math.min(c.line,i.line),p=Math.max(c.line,i.line),d=c.ch,v=i.ch,m=r[l].head.ch-d,g=v-d;m>0&&g<=0?(d++,s||v--):m<0&&g>=0?(d--,a||v++):m<0&&g==-1&&(d--,v++);for(var y=h;y<=p;y++){var b={anchor:new w(y,d),head:new w(y,v)};n.push(b)}return e.setSelections(n),t.ch=v,c.ch=d,c}function $t(e,t,n){var r=[];for(var i=0;i<n;i++){var s=Mt(t,i,0);r.push({anchor:s,head:s})}e.setSelections(r,0)}function Jt(e,t,n){for(var r=0;r<e.length;r++){var i=n!="head"&&jt(e[r].anchor,t),s=n!="anchor"&&jt(e[r].head,t);if(i||s)return r}return-1}function Kt(e,t){var n=t.lastSelection,r=function(){var t=e.listSelections(),n=t[0],r=t[t.length-1],i=Ft(n.anchor,n.head)?n.anchor:n.head,s=Ft(r.anchor,r.head)?r.head:r.anchor;return[i,s]},i=function(){var t=e.getCursor(),r=e.getCursor(),i=n.visualBlock;if(i){var s=i.width,o=i.height;r=new w(t.line+o,t.ch+s);var u=[];for(var a=t.line;a<r.line;a++){var f=new w(a,t.ch),l=new w(a,r.ch),c={anchor:f,head:l};u.push(c)}e.setSelections(u)}else{var h=n.anchorMark.find(),p=n.headMark.find(),d=p.line-h.line,v=p.ch-h.ch;r={line:r.line+d,ch:d?r.ch:v+r.ch},n.visualLine&&(t=new w(t.line,0),r=new w(r.line,Ut(e,r.line))),e.setSelection(t,r)}return[t,r]};return t.visualMode?r():i()}function Qt(e,t){var n=t.sel.anchor,r=t.sel.head;t.lastPastedText&&(r=e.posFromIndex(e.indexFromPos(n)+t.lastPastedText.length),t.lastPastedText=null),t.lastSelection={anchorMark:e.setBookmark(n),headMark:e.setBookmark(r),anchor:Bt(n),head:Bt(r),visualMode:t.visualMode,visualLine:t.visualLine,visualBlock:t.visualBlock}}function Gt(e,t,n,r){var i=e.state.vim.sel,s=r?t:i.head,o=r?t:i.anchor,u;return Ft(n,t)&&(u=n,n=t,t=u),Ft(s,o)?(s=It(t,s),o=qt(o,n)):(o=It(t,o),s=qt(s,n),s=Mt(s,0,-1),s.ch==-1&&s.line!=e.firstLine()&&(s=new w(s.line-1,Ut(e,s.line-1)))),[o,s]}function Yt(e,t,n){var r=e.state.vim;t=t||r.sel;var n=n||r.visualLine?"line":r.visualBlock?"block":"char",i=Zt(e,t,n);e.setSelections(i.ranges,i.primary)}function Zt(e,t,n,r){var i=Bt(t.head),s=Bt(t.anchor);if(n=="char"){var o=!r&&!Ft(t.head,t.anchor)?1:0,u=Ft(t.head,t.anchor)?1:0;return i=Mt(t.head,0,o),s=Mt(t.anchor,0,u),{ranges:[{anchor:s,head:i}],primary:0}}if(n=="line"){if(!Ft(t.head,t.anchor)){s.ch=0;var a=e.lastLine();i.line>a&&(i.line=a),i.ch=Ut(e,i.line)}else i.ch=0,s.ch=Ut(e,s.line);return{ranges:[{anchor:s,head:i}],primary:0}}if(n=="block"){var f=Math.min(s.line,i.line),l=s.ch,c=Math.max(s.line,i.line),h=i.ch;l<h?h+=1:l+=1;var p=c-f+1,d=i.line==f?0:p-1,v=[];for(var m=0;m<p;m++)v.push({anchor:new w(f+m,l),head:new w(f+m,h)});return{ranges:v,primary:d}}}function en(e){var t=e.getCursor("head");return e.getSelection().length==1&&(t=It(t,e.getCursor("anchor"))),t}function tn(e,t){var n=e.state.vim;t!==!1&&e.setCursor(At(e,n.sel.head)),Qt(e,n),n.visualMode=!1,n.visualLine=!1,n.visualBlock=!1,n.insertMode||m.signal(e,"vim-mode-change",{mode:"normal"})}function nn(e,t,n){var r=e.getRange(t,n);if(/\n\s*$/.test(r)){var i=r.split("\n");i.pop();var s;for(var s=i.pop();i.length>0&&s&&U(s);s=i.pop())n.line--,n.ch=0;s?(n.line--,n.ch=Ut(e,n.line)):n.ch=0}}function rn(e,t,n){t.ch=0,n.ch=0,n.line++}function sn(e){if(!e)return 0;var t=e.search(/\S/);return t==-1?e.length:t}function on(e,t,n){var r=t.inclusive,i=t.innerWord,s=t.bigWord,o=t.noSymbol,u=t.multiline,a=n||en(e),f=e.getLine(a.line),l=f,c=a.line,h=c,p=a.ch,d,v=o?O[0]:M[0];if(i&&/\s/.test(f.charAt(p)))v=function(e){return/\s/.test(e)};else{while(!v(f.charAt(p))){p++;if(p>=f.length){if(!u)return null;p--,d=pn(e,a,!0,s,!0);break}}s?v=M[0]:(v=O[0],v(f.charAt(p))||(v=O[1]))}var m=p,g=p;while(v(f.charAt(g))&&g>=0)g--;g++;if(d)m=d.to,h=d.line,l=e.getLine(h),!l&&m==0&&m++;else while(v(f.charAt(m))&&m<f.length)m++;if(r){var y=m,b=a.ch<=g&&/\s/.test(f.charAt(a.ch));if(!b)while(/\s/.test(l.charAt(m))&&m<l.length)m++;if(y==m||b){var E=g;while(/\s/.test(f.charAt(g-1))&&g>0)g--;!g&&!b&&(g=E)}}return{start:new w(c,g),end:new w(h,m)}}function un(e,t,n){var r=t;if(!m.findMatchingTag||!m.findEnclosingTag)return{start:r,end:r};var i=m.findMatchingTag(e,t)||m.findEnclosingTag(e,t);return!i||!i.open||!i.close?{start:r,end:r}:n?{start:i.open.from,end:i.close.to}:{start:i.open.to,end:i.close.from}}function an(e,t,n){jt(t,n)||Z.jumpList.add(e,t,n)}function fn(e,t){Z.lastCharacterSearch.increment=e,Z.lastCharacterSearch.forward=t.forward,Z.lastCharacterSearch.selectedCharacter=t.selectedCharacter}function hn(e,t,n,r){var i=Bt(e.getCursor()),s=n?1:-1,o=n?e.lineCount():-1,u=i.ch,a=i.line,f=e.getLine(a),l={lineText:f,nextCh:f.charAt(u),lastCh:null,index:u,symb:r,reverseSymb:(n?{")":"(","}":"{"}:{"(":")","{":"}"})[r],forward:n,depth:0,curMoveThrough:!1},c=ln[r];if(!c)return i;var h=cn[c].init,p=cn[c].isComplete;h&&h(l);while(a!==o&&t){l.index+=s,l.nextCh=l.lineText.charAt(l.index);if(!l.nextCh){a+=s,l.lineText=e.getLine(a)||"";if(s>0)l.index=0;else{var d=l.lineText.length;l.index=d>0?d-1:0}l.nextCh=l.lineText.charAt(l.index)}p(l)&&(i.line=a,i.ch=l.index,t--)}return l.nextCh||l.curMoveThrough?new w(a,l.index):i}function pn(e,t,n,r,i){var s=t.line,o=t.ch,u=e.getLine(s),a=n?1:-1,f=r?M:O;if(i&&u==""){s+=a,u=e.getLine(s);if(!j(e,s))return null;o=n?0:u.length}for(;;){if(i&&u=="")return{from:0,to:0,line:s};var l=a>0?u.length:-1,c=l,h=l;while(o!=l){var p=!1;for(var d=0;d<f.length&&!p;++d)if(f[d](u.charAt(o))){c=o;while(o!=l&&f[d](u.charAt(o)))o+=a;h=o,p=c!=h;if(c==t.ch&&s==t.line&&h==c+a)continue;return{from:Math.min(c,h+1),to:Math.max(c,h),line:s}}p||(o+=a)}s+=a;if(!j(e,s))return null;u=e.getLine(s),o=a>0?0:u.length}}function dn(e,t,n,r,i,s){var o=Bt(t),u=[];(r&&!i||!r&&i)&&n++;var a=!r||!i;for(var f=0;f<n;f++){var l=pn(e,t,r,s,a);if(!l){var c=Ut(e,e.lastLine());u.push(r?{line:e.lastLine(),from:c,to:c}:{line:0,from:0,to:0});break}u.push(l),t=new w(l.line,r?l.to-1:l.from)}var h=u.length!=n,p=u[0],d=u.pop();return r&&!i?(!h&&(p.from!=o.ch||p.line!=o.line)&&(d=u.pop()),new w(d.line,d.from)):r&&i?new w(d.line,d.to-1):!r&&i?(!h&&(p.to!=o.ch||p.line!=o.line)&&(d=u.pop()),new w(d.line,d.to)):new w(d.line,d.from)}function vn(e,t,n,r,i){var s=t,o=new w(s.line+n.repeat-1,Infinity),u=e.clipPos(o);return u.ch--,i||(r.lastHPos=Infinity,r.lastHSPos=e.charCoords(u,"div").left),o}function mn(e,t,n,r,i){var s=i||e.getCursor(),o=s.ch,u;for(var a=0;a<t;a++){var f=e.getLine(s.line);u=bn(o,f,r,n,!0);if(u==-1)return null;o=u}return new w(e.getCursor().line,u)}function gn(e,t){var n=e.getCursor().line;return At(e,new w(n,t-1))}function yn(e,t,n,r){if(!W(n,_)&&!P.test(n))return;t.marks[n]&&t.marks[n].clear(),t.marks[n]=e.setBookmark(r)}function bn(e,t,n,r,i){var s;return r?(s=t.indexOf(n,e+1),s!=-1&&!i&&(s-=1)):(s=t.lastIndexOf(n,e-1),s!=-1&&!i&&(s+=1)),s}function wn(e,t,n,r,i){function c(t){return!/\S/.test(e.getLine(t))}function h(e,t,n){return n?c(e)!=c(e+t):!c(e)&&c(e+t)}function p(t){r=r>0?1:-1;var n=e.ace.session.getFoldLine(t);n&&t+r>n.start.row&&t+r<n.end.row&&(r=(r>0?n.end.row:n.start.row)-t)}var s=t.line,o=e.firstLine(),u=e.lastLine(),a,f,l=s;if(r){while(o<=l&&l<=u&&n>0)p(l),h(l,r)&&n--,l+=r;return new w(l,0)}var d=e.state.vim;if(d.visualLine&&h(s,1,!0)){var v=d.sel.anchor;h(v.line,-1,!0)&&(!i||v.line!=s)&&(s+=1)}var m=c(s);for(l=s;l<=u&&n;l++)h(l,1,!0)&&(!i||c(l)!=m)&&n--;f=new w(l,0),l>u&&!m?m=!0:i=!1;for(l=s;l>o;l--)if(!i||c(l)==m||l==s)if(h(l,-1,!0))break;return a=new w(l,0),{start:a,end:f}}function En(e,t,n,r,i){function s(e){e.pos+e.dir<0||e.pos+e.dir>=e.line.length?e.line=null:e.pos+=e.dir}function o(e,t,n,r){var o=e.getLine(t),u={line:o,ln:t,pos:n,dir:r};if(u.line==="")return{ln:u.ln,pos:u.pos};var a=u.pos;s(u);while(u.line!==null){a=u.pos;if(z(u.line[u.pos])){if(!i)return{ln:u.ln,pos:u.pos+1};s(u);while(u.line!==null){if(!U(u.line[u.pos]))break;a=u.pos,s(u)}return{ln:u.ln,pos:a+1}}s(u)}return{ln:u.ln,pos:a+1}}function u(e,t,n,r){var o=e.getLine(t),u={line:o,ln:t,pos:n,dir:r};if(u.line==="")return{ln:u.ln,pos:u.pos};var a=u.pos;s(u);while(u.line!==null){if(!U(u.line[u.pos])&&!z(u.line[u.pos]))a=u.pos;else if(z(u.line[u.pos]))return i?U(u.line[u.pos+1])?{ln:u.ln,pos:u.pos+1}:{ln:u.ln,pos:a}:{ln:u.ln,pos:a};s(u)}return u.line=o,i&&U(u.line[u.pos])?{ln:u.ln,pos:u.pos}:{ln:u.ln,pos:a}}var a={ln:t.line,pos:t.ch};while(n>0)r<0?a=u(e,a.ln,a.pos,r):a=o(e,a.ln,a.pos,r),n--;return new w(a.ln,a.pos)}function Sn(e,t,n,r){function i(e,t){if(t.pos+t.dir<0||t.pos+t.dir>=t.line.length){t.ln+=t.dir;if(!j(e,t.ln)){t.line=null,t.ln=null,t.pos=null;return}t.line=e.getLine(t.ln),t.pos=t.dir>0?0:t.line.length-1}else t.pos+=t.dir}function s(e,t,n,r){var s=e.getLine(t),o=s==="",u={line:s,ln:t,pos:n,dir:r},a={ln:u.ln,pos:u.pos},f=u.line==="";i(e,u);while(u.line!==null){a.ln=u.ln,a.pos=u.pos;if(u.line===""&&!f)return{ln:u.ln,pos:u.pos};if(o&&u.line!==""&&!U(u.line[u.pos]))return{ln:u.ln,pos:u.pos};z(u.line[u.pos])&&!o&&(u.pos===u.line.length-1||U(u.line[u.pos+1]))&&(o=!0),i(e,u)}var s=e.getLine(a.ln);a.pos=0;for(var l=s.length-1;l>=0;--l)if(!U(s[l])){a.pos=l;break}return a}function o(e,t,n,r){var s=e.getLine(t),o={line:s,ln:t,pos:n,dir:r},u={ln:o.ln,pos:null},a=o.line==="";i(e,o);while(o.line!==null){if(o.line===""&&!a)return u.pos!==null?u:{ln:o.ln,pos:o.pos};if(!(!z(o.line[o.pos])||u.pos===null||o.ln===u.ln&&o.pos+1===u.pos))return u;o.line!==""&&!U(o.line[o.pos])&&(a=!1,u={ln:o.ln,pos:o.pos}),i(e,o)}var s=e.getLine(u.ln);u.pos=0;for(var f=0;f<s.length;++f)if(!U(s[f])){u.pos=f;break}return u}var u={ln:t.line,pos:t.ch};while(n>0)r<0?u=o(e,u.ln,u.pos,r):u=s(e,u.ln,u.pos,r),n--;return new w(u.ln,u.pos)}function xn(e,t,n,r){var i=t,s,o,u={"(":/[()]/,")":/[()]/,"[":/[[\]]/,"]":/[[\]]/,"{":/[{}]/,"}":/[{}]/,"<":/[<>]/,">":/[<>]/}[n],a={"(":"(",")":"(","[":"[","]":"[","{":"{","}":"{","<":"<",">":"<"}[n],f=e.getLine(i.line).charAt(i.ch),l=f===a?1:0;s=e.scanForBracket(new w(i.line,i.ch+l),-1,undefined,{bracketRegex:u}),o=e.scanForBracket(new w(i.line,i.ch+l),1,undefined,{bracketRegex:u});if(!s||!o)return null;s=s.pos,o=o.pos;if(s.line==o.line&&s.ch>o.ch||s.line>o.line){var c=s;s=o,o=c}return r?o.ch+=1:s.ch+=1,{start:s,end:o}}function Tn(e,t,n,r){var i=Bt(t),s=e.getLine(i.line),o=s.split(""),u,a,f,l,c=o.indexOf(n);if(i.ch<c)i.ch=c;else if(c<i.ch&&o[i.ch]==n){var h=/string/.test(e.getTokenTypeAt(Mt(t,0,1))),p=/string/.test(e.getTokenTypeAt(t)),d=h&&!p;d||(a=i.ch,--i.ch)}if(o[i.ch]==n&&!a)u=i.ch+1;else for(f=i.ch;f>-1&&!u;f--)o[f]==n&&(u=f+1);if(u&&!a)for(f=u,l=o.length;f<l&&!a;f++)o[f]==n&&(a=f);return!u||!a?{start:i,end:i}:(r&&(--u,++a),{start:new w(i.line,u),end:new w(i.line,a)})}function Nn(){}function Cn(e){var t=e.state.vim;return t.searchState_||(t.searchState_=new Nn)}function kn(e){return An(e,"/")}function Ln(e){return On(e,"/")}function An(e,t){var n=On(e,t)||[];if(!n.length)return[];var r=[];if(n[0]!==0)return;for(var i=0;i<n.length;i++)typeof n[i]=="number"&&r.push(e.substring(n[i]+1,n[i+1]));return r}function On(e,t){t||(t="/");var n=!1,r=[];for(var i=0;i<e.length;i++){var s=e.charAt(i);!n&&s==t&&r.push(i),n=!n&&s=="\\"}return r}function Mn(e){var t="|(){",n="}",r=!1,i=[];for(var s=-1;s<e.length;s++){var o=e.charAt(s)||"",u=e.charAt(s+1)||"",a=u&&t.indexOf(u)!=-1;r?((o!=="\\"||!a)&&i.push(o),r=!1):o==="\\"?(r=!0,u&&n.indexOf(u)!=-1&&(a=!0),(!a||u==="\\")&&i.push(o)):(i.push(o),a&&u!=="\\"&&i.push("\\"))}return i.join("")}function Dn(e){var t=!1,n=[];for(var r=-1;r<e.length;r++){var i=e.charAt(r)||"",s=e.charAt(r+1)||"";_n[i+s]?(n.push(_n[i+s]),r++):t?(n.push(i),t=!1):i==="\\"?(t=!0,q(s)||s==="$"?n.push("$"):s!=="/"&&s!=="\\"&&n.push("\\")):(i==="$"&&n.push("$"),n.push(i),s==="/"&&n.push("\\"))}return n.join("")}function Hn(e){var t=new m.StringStream(e),n=[];while(!t.eol()){while(t.peek()&&t.peek()!="\\")n.push(t.next());var r=!1;for(var i in Pn)if(t.match(i,!0)){r=!0,n.push(Pn[i]);break}r||n.push(t.next())}return n.join("")}function Bn(e,t,n){var r=Z.registerController.getRegister("/");r.setText(e);if(e instanceof RegExp)return e;var i=Ln(e),s,o;if(!i.length)s=e;else{s=e.substring(0,i[0]);var u=e.substring(i[0]);o=u.indexOf("i")!=-1}if(!s)return null;J("pcre")||(s=Mn(s)),n&&(t=/^[^A-Z]*$/.test(s));var a=new RegExp(s,t||o?"im":"m");return a}function jn(e){typeof e=="string"&&(e=document.createElement(e));for(var t,n=1;n<arguments.length;n++){if(!(t=arguments[n]))continue;typeof t!="object"&&(t=document.createTextNode(t));if(t.nodeType)e.appendChild(t);else for(var r in t){if(!Object.prototype.hasOwnProperty.call(t,r))continue;r[0]==="$"?e.style[r.slice(1)]=t[r]:e.setAttribute(r,t[r])}}return e}function Fn(e,t){var n=jn("div",{$color:"red",$whiteSpace:"pre","class":"cm-vim-message"},t);e.openNotification?e.openNotification(n,{bottom:!0,duration:5e3}):alert(n.innerText)}function In(e,t){return jn("div",{$display:"flex"},jn("span",{$fontFamily:"monospace",$whiteSpace:"pre",$flex:1},e,jn("input",{type:"text",autocorrect:"off",autocapitalize:"off",spellcheck:"false",$width:"100%"})),t&&jn("span",{$color:"#888"},t))}function qn(e,t){if(rt.length){t.value||(t.value=""),st=t;return}var n=In(t.prefix,t.desc);if(e.openDialog)e.openDialog(n,t.onClose,{onKeyDown:t.onKeyDown,onKeyUp:t.onKeyUp,bottom:!0,selectValueOnOpen:!1,value:t.value});else{var r="";typeof t.prefix!="string"&&t.prefix&&(r+=t.prefix.textContent),t.desc&&(r+=" "+t.desc),t.onClose(prompt(r,""))}}function Rn(e,t){if(e instanceof RegExp&&t instanceof RegExp){var n=["global","multiline","ignoreCase","source"];for(var r=0;r<n.length;r++){var i=n[r];if(e[i]!==t[i])return!1}return!0}return!1}function Un(e,t,n,r){if(!t)return;var i=Cn(e),s=Bn(t,!!n,!!r);if(!s)return;return Xn(e,s),Rn(s,i.getQuery())?s:(i.setQuery(s),s)}function zn(e){if(e.source.charAt(0)=="^")var t=!0;return{token:function(n){if(t&&!n.sol()){n.skipToEnd();return}var r=n.match(e,!1);if(r){if(r[0].length==0)return n.next(),"searching";if(!n.sol()){n.backUp(1);if(!e.exec(n.next()+r[0]))return n.next(),null}return n.match(e),"searching"}while(!n.eol()){n.next();if(n.match(e,!1))break}},query:e}}function Xn(e,t){clearTimeout(Wn);var n=Cn(e);n.highlightTimeout=Wn,Wn=setTimeout(function(){if(!e.state.vim)return;var n=Cn(e);n.highlightTimeout=null;var r=n.getOverlay();if(!r||t!=r.query)r&&e.removeOverlay(r),r=zn(t),e.addOverlay(r),e.showMatchesOnScrollbar&&(n.getScrollbarAnnotate()&&n.getScrollbarAnnotate().clear(),n.setScrollbarAnnotate(e.showMatchesOnScrollbar(t))),n.setOverlay(r)},50)}function Vn(e,t,n,r){return r===undefined&&(r=1),e.operation(function(){var i=e.getCursor(),s=e.getSearchCursor(n,i);for(var o=0;o<r;o++){var u=s.find(t);if(o==0&&u&&jt(s.from(),i)){var a=t?s.from():s.to();u=s.find(t),u&&!u[0]&&jt(s.from(),a)&&e.getLine(a.line).length==a.ch&&(u=s.find(t))}if(!u){s=e.getSearchCursor(n,t?new w(e.lastLine()):new w(e.firstLine(),0));if(!s.find(t))return}}return s.from()})}function $n(e,t,n,r,i){return r===undefined&&(r=1),e.operation(function(){var s=e.getCursor(),o=e.getSearchCursor(n,s),u=o.find(!t);!i.visualMode&&u&&jt(o.from(),s)&&o.find(!t);for(var a=0;a<r;a++){u=o.find(t);if(!u){o=e.getSearchCursor(n,t?new w(e.lastLine()):new w(e.firstLine(),0));if(!o.find(t))return}}return[o.from(),o.to()]})}function Jn(e){var t=Cn(e);t.highlightTimeout&&(clearTimeout(t.highlightTimeout),t.highlightTimeout=null),e.removeOverlay(Cn(e).getOverlay()),t.setOverlay(null),t.getScrollbarAnnotate()&&(t.getScrollbarAnnotate().clear(),t.setScrollbarAnnotate(null))}function Kn(e,t,n){return typeof e!="number"&&(e=e.line),t instanceof Array?W(e,t):typeof n=="number"?e>=t&&e<=n:e==t}function Qn(e){var t=e.ace.renderer;return{top:t.getFirstFullyVisibleRow(),bottom:t.getLastFullyVisibleRow()}}function Gn(e,t,n){if(n=="'"||n=="`")return Z.jumpList.find(e,-1)||new w(0,0);if(n==".")return Yn(e);var r=t.marks[n];return r&&r.find()}function Yn(e){if(e.getLastEditEnd)return e.getLastEditEnd();var t=e.doc.history.done;for(var n=t.length;n--;)if(t[n].changes)return Bt(t[n].changes[0].to)}function nr(e,t,n,r,i,s,o,u,a){function p(){e.operation(function(){while(!f)d(),g();y()})}function d(){var t=e.getRange(s.from(),s.to()),n=t.replace(o,u),r=s.to().line;s.replace(n),c=s.to().line,i+=c-r,h=c<r}function v(){var e=l&&Bt(s.to()),t=s.findNext();return t&&!t[0]&&e&&jt(s.from(),e)&&(t=s.findNext()),t}function g(){while(v()&&Kn(s.from(),r,i)){if(!n&&s.from().line==c&&!h)continue;e.scrollIntoView(s.from(),30),e.setSelection(s.from(),s.to()),l=s.from(),f=!1;return}f=!0}function y(t){t&&t(),e.focus();if(l){e.setCursor(l);var n=e.state.vim;n.exMode=!1,n.lastHPos=n.lastHSPos=l.ch}a&&a()}function b(t,n,r){m.e_stop(t);var i=ct(t);switch(i){case"y":d(),g();break;case"n":g();break;case"a":var s=a;a=undefined,e.operation(p),a=s;break;case"l":d();case"q":case"<Esc>":case"<C-c>":case"<C-[>":y(r)}return f&&y(r),!0}e.state.vim.exMode=!0;var f=!1,l,c,h;g();if(f){Fn(e,"No matches for "+o.source);return}if(!t){p(),a&&a();return}qn(e,{prefix:jn("span","replace with ",jn("strong",u)," (y/n/a/q/l)"),onKeyDown:b})}function rr(e,t){var n=e.state.vim,r=Z.macroModeState,i=Z.registerController.getRegister("."),s=r.isPlaying,o=r.lastInsertModeChanges;s||(e.off("change",lr),n.insertEnd&&n.insertEnd.clear(),n.insertEnd=null,m.off(e.getInputField(),"keydown",dr)),!s&&n.insertModeRepeat>1&&(vr(e,n,n.insertModeRepeat-1,!0),n.lastEditInputState.repeatOverride=n.insertModeRepeat),delete n.insertModeRepeat,n.insertMode=!1,t||e.setCursor(e.getCursor().line,e.getCursor().ch-1),e.setOption("keyMap","vim"),e.setOption("disableInput",!0),e.toggleOverwrite(!1),i.setText(o.changes.join("")),m.signal(e,"vim-mode-change",{mode:"normal"}),r.isRecording&&ar(r)}function ir(e){S.unshift(e)}function sr(e,t,n,r,i){var s={keys:e,type:t};s[t]=n,s[t+"Args"]=r;for(var o in i)s[o]=i[o];ir(s)}function or(e,t,n,r){var i=Z.registerController.getRegister(r);if(r==":"){i.keyBuffer[0]&&tr.processCommand(e,i.keyBuffer[0]),n.isPlaying=!1;return}var s=i.keyBuffer,o=0;n.isPlaying=!0,n.replaySearchQueries=i.searchQueries.slice(0);for(var u=0;u<s.length;u++){var a=s[u],f,l;while(a){f=/<\w+-.+?>|<\w+>|./.exec(a),l=f[0],a=a.substring(f.index+l.length),nt.handleKey(e,l,"macro");if(t.insertMode){var c=i.insertModeChanges[o++].changes;Z.macroModeState.lastInsertModeChanges.changes=c,gr(e,c,1),rr(e)}}}n.isPlaying=!1}function ur(e,t){if(e.isPlaying)return;var n=e.latestRegister,r=Z.registerController.getRegister(n);r&&r.pushText(t)}function ar(e){if(e.isPlaying)return;var t=e.latestRegister,n=Z.registerController.getRegister(t);n&&n.pushInsertModeChanges&&n.pushInsertModeChanges(e.lastInsertModeChanges)}function fr(e,t){if(e.isPlaying)return;var n=e.latestRegister,r=Z.registerController.getRegister(n);r&&r.pushSearchQuery&&r.pushSearchQuery(t)}function lr(e,t){var n=Z.macroModeState,r=n.lastInsertModeChanges;if(!n.isPlaying){var i=e.state.vim;while(t){r.expectCursorActivityForChange=!0;if(r.ignoreCount>1)r.ignoreCount--;else if(t.origin=="+input"||t.origin=="paste"||t.origin===undefined){var s=e.listSelections().length;s>1&&(r.ignoreCount=s);var o=t.text.join("\n");r.maybeReset&&(r.changes=[],r.maybeReset=!1);if(o)if(e.state.overwrite&&!/\n/.test(o))r.changes.push([o]);else{if(o.length>1){var u=i&&i.insertEnd&&i.insertEnd.find(),a=e.getCursor();if(u&&u.line==a.line){var f=u.ch-a.ch;f>0&&f<o.length&&(r.changes.push([o,f]),o="")}}o&&r.changes.push(o)}}t=t.next}}}function cr(e){var t=e.state.vim;if(t.insertMode){var n=Z.macroModeState;if(n.isPlaying)return;var r=n.lastInsertModeChanges;r.expectCursorActivityForChange?r.expectCursorActivityForChange=!1:(r.maybeReset=!0,t.insertEnd&&t.insertEnd.clear(),t.insertEnd=e.setBookmark(e.getCursor(),{insertLeft:!0}))}else e.curOp.isVimOp||hr(e,t)}function hr(e,t,n){var r=e.getCursor("anchor"),i=e.getCursor("head");t.visualMode&&!e.somethingSelected()?tn(e,!1):!t.visualMode&&!t.insertMode&&e.somethingSelected()&&(t.visualMode=!0,t.visualLine=!1,m.signal(e,"vim-mode-change",{mode:"visual"}));if(t.visualMode){var s=Ft(i,r)?0:-1,o=Ft(i,r)?-1:0;i=Mt(i,0,s),r=Mt(r,0,o),t.sel={anchor:r,head:i},yn(e,t,"<",It(i,r)),yn(e,t,">",qt(i,r))}else!t.insertMode&&!n&&(t.lastHPos=e.getCursor().ch)}function pr(e,t){this.keyName=e,this.key=t.key,this.ctrlKey=t.ctrlKey,this.altKey=t.altKey,this.metaKey=t.metaKey,this.shiftKey=t.shiftKey}function dr(e){var t=Z.macroModeState,n=t.lastInsertModeChanges,r=m.keyName?m.keyName(e):e.key;if(!r)return;if(r.indexOf("Delete")!=-1||r.indexOf("Backspace")!=-1)n.maybeReset&&(n.changes=[],n.maybeReset=!1),n.changes.push(new pr(r,e))}function vr(e,t,n,r){function u(){s?Et.processAction(e,t,t.lastEditActionCommand):Et.evalInput(e,t)}function a(n){if(i.lastInsertModeChanges.changes.length>0){n=t.lastEditActionCommand?n:1;var r=i.lastInsertModeChanges;gr(e,r.changes,n)}}var i=Z.macroModeState;i.isPlaying=!0;var s=!!t.lastEditActionCommand,o=t.inputState;t.inputState=t.lastEditInputState;if(s&&t.lastEditActionCommand.interlaceInsertRepeat)for(var f=0;f<n;f++)u(),a(1);else r||u(),a(n);t.inputState=o,t.insertMode&&!r&&rr(e),i.isPlaying=!1}function mr(e,t){m.lookupKey(t,"vim-insert",function(n){return typeof n=="string"?m.commands[n](e):n(e),!0})}function gr(e,t,n){var r=e.getCursor("head"),i=Z.macroModeState.lastInsertModeChanges.visualBlock;i&&($t(e,r,i+1),n=e.listSelections().length,e.setCursor(r));for(var s=0;s<n;s++){i&&e.setCursor(Mt(r,s,0));for(var o=0;o<t.length;o++){var u=t[o];if(u instanceof pr)mr(e,u.keyName,u);else if(typeof u=="string")e.replaceSelection(u);else{var a=e.getCursor(),f=Mt(a,0,u[0].length-(u[1]||0));e.replaceRange(u[0],a,u[1]?a:f),e.setCursor(f)}}}i&&e.setCursor(Mt(r,0,1))}function br(e,t,n,r){t.length>1&&t[0]=="n"&&(t=t.replace("numpad","")),t=yr[t]||t;var i="";n.ctrlKey&&(i+="C-"),n.altKey&&(i+="A-"),(i||t.length>1)&&n.shiftKey&&(i+="S-");if(r&&!r.expectLiteralNext&&t.length==1)if(N.keymap&&t in N.keymap){if(N.remapCtrl!==!1||!i)t=N.keymap[t]}else if(t.charCodeAt(0)>255){var s=n.code&&n.code.slice(-1)||"";n.shiftKey||(s=s.toLowerCase()),s&&(t=s)}return i+=t,i.length>1&&(i="<"+i+">"),i}function Er(e){var t=new e.constructor;return Object.keys(e).forEach(function(n){if(n=="insertEnd")return;var r=e[n];Array.isArray(r)?r=r.slice():r&&typeof r=="object"&&r.constructor!=Object&&(r=Er(r)),t[n]=r}),e.sel&&(t.sel={head:e.sel.head&&Bt(e.sel.head),anchor:e.sel.anchor&&Bt(e.sel.anchor)}),t}function Sr(e,t,n){var r=!1,i=nt.maybeInitVimState_(e),s=i.visualBlock||i.wasInVisualBlock,o=e.ace.inMultiSelectMode;i.wasInVisualBlock&&!o?i.wasInVisualBlock=!1:o&&i.visualBlock&&(i.wasInVisualBlock=!0);if(t=="<Esc>"&&!i.insertMode&&!i.visualMode&&o)e.ace.exitMultiSelectMode();else if(s||!o||e.ace.inVirtualSelectionMode)r=nt.handleKey(e,t,n);else{var u=Er(i),a=i.inputState.changeQueueList||[];e.operation(function(){e.curOp.isVimOp=!0;var s=0;e.ace.forEachSelection(function(){var i=e.ace.selection;e.state.vim.lastHPos=i.$desiredColumn==null?i.lead.column:i.$desiredColumn,e.state.vim.inputState.changeQueue=a[s];var o=e.getCursor("head"),f=e.getCursor("anchor"),l=Ft(o,f)?0:-1,c=Ft(o,f)?-1:0;o=Mt(o,0,l),f=Mt(f,0,c),e.state.vim.sel.head=o,e.state.vim.sel.anchor=f,r=wr(e,t,n),i.$desiredColumn=e.state.vim.lastHPos==-1?null:e.state.vim.lastHPos,e.ace.inVirtualSelectionMode&&(a[s]=e.state.vim.inputState.changeQueue),e.virtualSelectionMode()&&(e.state.vim=Er(u)),s++}),e.curOp.cursorActivity&&!r&&(e.curOp.cursorActivity=!1),i.status=e.state.vim.status,e.state.vim=i,i.inputState.changeQueueList=a,i.inputState.changeQueue=null},!0)}return r&&!i.visualMode&&!i.insert&&i.visualMode!=e.somethingSelected()&&hr(e,i,!0),r}function Tr(e,t){t.off("beforeEndOperation",Tr);var n=t.state.cm.vimCmd;n&&t.execCommand(n.exec?n:n.name,n.args),t.curOp=t.prevOp}var i=e("../range").Range,s=e("../lib/event_emitter").EventEmitter,o=e("../lib/dom"),u=e("../lib/oop"),a=e("../lib/keys"),f=e("../lib/event"),l=e("../search").Search,c=e("../lib/useragent"),h=e("../search_highlight").SearchHighlight,p=e("../commands/multi_select_commands"),d=e("../mode/text").Mode.prototype.tokenRe,v=e("../ext/hardwrap").hardWrap;e("../multi_select");var m=function(e){this.ace=e,this.state={},this.marks={},this.options={},this.$uid=0,this.onChange=this.onChange.bind(this),this.onSelectionChange=this.onSelectionChange.bind(this),this.onBeforeEndOperation=this.onBeforeEndOperation.bind(this),this.ace.on("change",this.onChange),this.ace.on("changeSelection",this.onSelectionChange),this.ace.on("beforeEndOperation",this.onBeforeEndOperation)};m.Pos=function(e,t){if(!(this instanceof w))return new w(e,t);this.line=e,this.ch=t},m.defineOption=function(e,t,n){},m.commands={redo:function(e){e.ace.redo()},undo:function(e){e.ace.undo()},newlineAndIndent:function(e){e.ace.insert("\n")},goLineLeft:function(e){e.ace.selection.moveCursorLineStart()},goLineRight:function(e){e.ace.selection.moveCursorLineEnd()}},m.keyMap={},m.addClass=m.rmClass=function(){},m.e_stop=m.e_preventDefault=f.stopEvent,m.keyName=function(e){var t=a[e.keyCode]||e.key||"";return t.length==1&&(t=t.toUpperCase()),t=f.getModifierString(e).replace(/(^|-)\w/g,function(e){return e.toUpperCase()})+t,t},m.keyMap["default"]=function(e){return function(t){var n=t.ace.commands.commandKeyBinding[e.toLowerCase()];return n&&t.ace.execCommand(n)!==!1}},m.lookupKey=function Nr(e,t,n){t||(t="default"),typeof t=="string"&&(t=m.keyMap[t]||m.keyMap["default"]);var r=typeof t=="function"?t(e):t[e];if(r===!1)return"nothing";if(r==="...")return"multi";if(r!=null&&n(r))return"handled";if(t.fallthrough){if(!Array.isArray(t.fallthrough))return Nr(e,t.fallthrough,n);for(var i=0;i<t.fallthrough.length;i++){var s=Nr(e,t.fallthrough[i],n);if(s)return s}}},m.findMatchingTag=function(e,t){return e.findMatchingTag(t)},m.findEnclosingTag=function(e,t){},m.signal=function(e,t,n){return e._signal(t,n)},m.on=f.addListener,m.off=f.removeListener,m.isWordChar=function(e){return e<""?/^\w$/.test(e):(d.lastIndex=0,d.test(e))},function(){u.implement(m.prototype,s),this.destroy=function(){this.ace.off("change",this.onChange),this.ace.off("changeSelection",this.onSelectionChange),this.ace.off("beforeEndOperation",this.onBeforeEndOperation),this.removeOverlay()},this.virtualSelectionMode=function(){return this.ace.inVirtualSelectionMode&&this.ace.selection.index},this.onChange=function(e){this.$lineHandleChanges&&this.$lineHandleChanges.push(e);var t={text:e.action[0]=="i"?e.lines:[]},n=this.curOp=this.curOp||{};n.changeHandlers||(n.changeHandlers=this._eventRegistry.change&&this._eventRegistry.change.slice()),n.lastChange?n.lastChange.next=n.lastChange=t:n.lastChange=n.change=t,this.$updateMarkers(e)},this.onSelectionChange=function(){var e=this.curOp=this.curOp||{};e.cursorActivityHandlers||(e.cursorActivityHandlers=this._eventRegistry.cursorActivity&&this._eventRegistry.cursorActivity.slice()),this.curOp.cursorActivity=!0,this.ace.inMultiSelectMode&&this.ace.keyBinding.removeKeyboardHandler(p.keyboardHandler)},this.operation=function(e,t){if(!t&&this.curOp||t&&this.curOp&&this.curOp.force)return e();(t||!this.ace.curOp)&&this.curOp&&this.onBeforeEndOperation();if(!this.ace.curOp){var n=this.ace.prevOp;this.ace.startOperation({command:{name:"vim",scrollIntoView:"cursor"}})}var r=this.curOp=this.curOp||{};this.curOp.force=t;var i=e();return this.ace.curOp&&this.ace.curOp.command.name=="vim"&&(this.state.dialog&&(this.ace.curOp.command.scrollIntoView=this.ace.curOp.vimDialogScroll),this.ace.endOperation(),!r.cursorActivity&&!r.lastChange&&n&&(this.ace.prevOp=n)),(t||!this.ace.curOp)&&this.curOp&&this.onBeforeEndOperation(),i},this.onBeforeEndOperation=function(){var e=this.curOp;e&&(e.change&&this.signal("change",e.change,e),e&&e.cursorActivity&&this.signal("cursorActivity",null,e),this.curOp=null)},this.signal=function(e,t,n){var r=n?n[e+"Handlers"]:(this._eventRegistry||{})[e];if(!r)return;r=r.slice();for(var i=0;i<r.length;i++)r[i](this,t)},this.firstLine=function(){return 0},this.lastLine=function(){return this.ace.session.getLength()-1},this.lineCount=function(){return this.ace.session.getLength()},this.setCursor=function(e,t){typeof e=="object"&&(t=e.ch,e=e.line);var n=!this.curOp&&!this.ace.inVirtualSelectionMode;this.ace.inVirtualSelectionMode||this.ace.exitMultiSelectMode(),this.ace.session.unfold({row:e,column:t}),this.ace.selection.moveTo(e,t),n&&(this.ace.renderer.scrollCursorIntoView(),this.ace.endOperation())},this.getCursor=function(e){var t=this.ace.selection,n=e=="anchor"?t.isEmpty()?t.lead:t.anchor:e=="head"||!e?t.lead:t.getRange()[e];return y(n)},this.listSelections=function(e){var t=this.ace.multiSelect.rangeList.ranges;return!t.length||this.ace.inVirtualSelectionMode?[{anchor:this.getCursor("anchor"),head:this.getCursor("head")}]:t.map(function(e){return{anchor:this.clipPos(y(e.cursor==e.end?e.start:e.end)),head:this.clipPos(y(e.cursor))}},this)},this.setSelections=function(e,t){var n=this.ace.multiSelect,r=e.map(function(e){var t=g(e.anchor),n=g(e.head),r=i.comparePoints(t,n)<0?new i.fromPoints(t,n):new i.fromPoints(n,t);return r.cursor=i.comparePoints(r.start,n)?r.end:r.start,r});if(this.ace.inVirtualSelectionMode){this.ace.selection.fromOrientedRange(r[0]);return}t?r[t]&&r.push(r.splice(t,1)[0]):r=r.reverse(),n.toSingleRange(r[0].clone());var s=this.ace.session;for(var o=0;o<r.length;o++){var u=s.$clipRangeToDocument(r[o]);n.addRange(u)}},this.setSelection=function(e,t,n){var r=this.ace.selection;r.moveTo(e.line,e.ch),r.selectTo(t.line,t.ch),n&&n.origin=="*mouse"&&this.onBeforeEndOperation()},this.somethingSelected=function(e){return!this.ace.selection.isEmpty()},this.clipPos=function(e){var t=this.ace.session.$clipPositionToDocument(e.line,e.ch);return y(t)},this.foldCode=function(e){this.ace.session.$toggleFoldWidget(e.line,{})},this.markText=function(e){return{clear:function(){},find:function(){}}},this.$updateMarkers=function(e){var t=e.action=="insert",n=e.start,r=e.end,s=(r.row-n.row)*(t?1:-1),o=(r.column-n.column)*(t?1:-1);t&&(r=n);for(var u in this.marks){var a=this.marks[u],f=i.comparePoints(a,n);if(f<0)continue;if(f===0&&t)if(!a.$insertRight)f=1;else{if(a.bias!=1){a.bias=-1;continue}f=1}var l=t?f:i.comparePoints(a,r);if(l>0){a.row+=s,a.column+=a.row==r.row?o:0;continue}!t&&l<=0&&(a.row=n.row,a.column=n.column,l===0&&(a.bias=1))}};var e=function(e,t,n,r){this.cm=e,this.id=t,this.row=n,this.column=r,e.marks[this.id]=this};e.prototype.clear=function(){delete this.cm.marks[this.id]},e.prototype.find=function(){return y(this)},this.setBookmark=function(t,n){var r=new e(this,this.$uid++,t.line,t.ch);if(!n||!n.insertLeft)r.$insertRight=!0;return this.marks[r.id]=r,r},this.moveH=function(e,t){if(t=="char"){var n=this.ace.selection;n.clearSelection(),n.moveCursorBy(0,e)}},this.findPosV=function(e,t,n,r){if(n=="page"){var i=this.ace.renderer,s=i.layerConfig;t*=Math.floor(s.height/s.lineHeight),n="line"}if(n=="line"){var o=this.ace.session.documentToScreenPosition(e.line,e.ch);r!=null&&(o.column=r),o.row+=t,o.row=Math.min(Math.max(0,o.row),this.ace.session.getScreenLength()-1);var u=this.ace.session.screenToDocumentPosition(o.row,o.column);return y(u)}debugger},this.charCoords=function(e,t){if(t=="div"||!t){var n=this.ace.session.documentToScreenPosition(e.line,e.ch);return{left:n.column,top:n.row}}if(t=="local"){var r=this.ace.renderer,n=this.ace.session.documentToScreenPosition(e.line,e.ch),i=r.layerConfig.lineHeight,s=r.layerConfig.characterWidth,o=i*n.row;return{left:n.column*s,top:o,bottom:o+i}}},this.coordsChar=function(e,t){var n=this.ace.renderer;if(t=="local"){var r=Math.max(0,Math.floor(e.top/n.lineHeight)),i=Math.max(0,Math.floor(e.left/n.characterWidth)),s=n.session.screenToDocumentPosition(r,i);return y(s)}if(t=="div")throw"not implemented"},this.getSearchCursor=function(e,t,n){var r=!1,i=!1;e instanceof RegExp&&!e.global&&(r=!e.ignoreCase,e=e.source,i=!0),e=="\\n"&&(e="\n",i=!1);var s=new l;t.ch==undefined&&(t.ch=Number.MAX_VALUE);var o={row:t.line,column:t.ch},u=this,a=null;return{findNext:function(){return this.find(!1)},findPrevious:function(){return this.find(!0)},find:function(t){s.setOptions({needle:e,caseSensitive:r,wrap:!1,backwards:t,regExp:i,start:a||o});var n=s.find(u.ace.session);return a=n,a&&[!a.isEmpty()]},from:function(){return a&&y(a.start)},to:function(){return a&&y(a.end)},replace:function(e){a&&(a.end=u.ace.session.doc.replace(a,e))}}},this.scrollTo=function(e,t){var n=this.ace.renderer,r=n.layerConfig,i=r.maxHeight;i-=(n.$size.scrollerHeight-n.lineHeight)*n.$scrollPastEnd,t!=null&&this.ace.session.setScrollTop(Math.max(0,Math.min(t,i))),e!=null&&this.ace.session.setScrollLeft(Math.max(0,Math.min(e,r.width)))},this.scrollInfo=function(){return 0},this.scrollIntoView=function(e,t){if(e){var n=this.ace.renderer,r={top:0,bottom:t};n.scrollCursorIntoView(g(e),n.lineHeight*2/n.$size.scrollerHeight,r)}},this.getLine=function(e){return this.ace.session.getLine(e)},this.getRange=function(e,t){return this.ace.session.getTextRange(new i(e.line,e.ch,t.line,t.ch))},this.replaceRange=function(e,t,n){n||(n=t);var r=new i(t.line,t.ch,n.line,n.ch);return this.ace.session.$clipRangeToDocument(r),this.ace.session.replace(r,e)},this.replaceSelection=this.replaceSelections=function(e){var t=Array.isArray(e)&&e,n=this.ace.selection;if(this.ace.inVirtualSelectionMode){this.ace.session.replace(n.getRange(),t?e[0]||"":e);return}n.inVirtualSelectionMode=!0;var r=n.rangeList.ranges;r.length||(r=[this.ace.multiSelect.getRange()]);for(var i=r.length;i--;)this.ace.session.replace(r[i],t?e[i]||"":e);n.inVirtualSelectionMode=!1},this.getSelection=function(){return this.ace.getSelectedText()},this.getSelections=function(){return this.listSelections().map(function(e){return this.getRange(e.anchor,e.head)},this)},this.getInputField=function(){return this.ace.textInput.getElement()},this.getWrapperElement=function(){return this.ace.container};var t={indentWithTabs:"useSoftTabs",indentUnit:"tabSize",tabSize:"tabSize",firstLineNumber:"firstLineNumber",readOnly:"readOnly"};this.setOption=function(e,n){this.state[e]=n;switch(e){case"indentWithTabs":e=t[e],n=!n;break;case"keyMap":this.state.$keyMap=n;return;default:e=t[e]}e&&this.ace.setOption(e,n)},this.getOption=function(e){var n,r=t[e];r&&(n=this.ace.getOption(r));switch(e){case"indentWithTabs":return e=t[e],!n;case"keyMap":return this.state.$keyMap||"vim"}return r?n:this.state[e]},this.toggleOverwrite=function(e){return this.state.overwrite=e,this.ace.setOverwrite(e)},this.addOverlay=function(e){if(!this.$searchHighlight||!this.$searchHighlight.session){var t=new h(null,"ace_highlight-marker","text"),n=this.ace.session.addDynamicMarker(t);t.id=n.id,t.session=this.ace.session,t.destroy=function(e){t.session.off("change",t.updateOnChange),t.session.off("changeEditor",t.destroy),t.session.removeMarker(t.id),t.session=null},t.updateOnChange=function(e){var n=e.start.row;n==e.end.row?t.cache[n]=undefined:t.cache.splice(n,t.cache.length)},t.session.on("changeEditor",t.destroy),t.session.on("change",t.updateOnChange)}var r=new RegExp(e.query.source,"gmi");this.$searchHighlight=e.highlight=t,this.$searchHighlight.setRegexp(r),this.ace.renderer.updateBackMarkers()},this.removeOverlay=function(e){this.$searchHighlight&&this.$searchHighlight.session&&this.$searchHighlight.destroy()},this.getScrollInfo=function(){var e=this.ace.renderer,t=e.layerConfig;return{left:e.scrollLeft,top:e.scrollTop,height:t.maxHeight,width:t.width,clientHeight:t.height,clientWidth:t.width}},this.getValue=function(){return this.ace.getValue()},this.setValue=function(e){return this.ace.setValue(e,-1)},this.getTokenTypeAt=function(e){var t=this.ace.session.getTokenAt(e.line,e.ch);return t&&/comment|string/.test(t.type)?"string":""},this.findMatchingBracket=function(e){var t=this.ace.session.findMatchingBracket(g(e));return{to:t&&y(t)}},this.findMatchingTag=function(e){var t=this.ace.session.getMatchingTags(g(e));if(!t)return;return{open:{from:y(t.openTag.start),to:y(t.openTag.end)},close:{from:y(t.closeTag.start),to:y(t.closeTag.end)}}},this.indentLine=function(e,t){t===!0?this.ace.session.indentRows(e,e,"	"):t===!1&&this.ace.session.outdentRows(new i(e,0,e,0))},this.indexFromPos=function(e){return this.ace.session.doc.positionToIndex(g(e))},this.posFromIndex=function(e){return y(this.ace.session.doc.indexToPosition(e))},this.focus=function(e){return this.ace.textInput.focus()},this.blur=function(e){return this.ace.blur()},this.defaultTextHeight=function(e){return this.ace.renderer.layerConfig.lineHeight},this.scanForBracket=function(e,t,n,r){var i=r.bracketRegex.source,s=/paren|text|operator|tag/;if(t==1)var o=this.ace.session.$findClosingBracket(i.slice(1,2),g(e),s);else{var o=this.ace.session.$findOpeningBracket(i.slice(-2,-1),{row:e.line,column:e.ch+1},s);!o&&r.bracketRegex&&r.bracketRegex.test(this.getLine(e.line)[e.ch-1])&&(o={row:e.line,column:e.ch-1})}return o&&{pos:y(o)}},this.refresh=function(){return this.ace.resize(!0)},this.getMode=function(){return{name:this.getOption("mode")}},this.execCommand=function(e){if(m.commands.hasOwnProperty(e))return m.commands[e](this);if(e=="indentAuto")return this.ace.execCommand("autoindent");console.log(e+" is not implemented")},this.getLineNumber=function(e){var t=this.$lineHandleChanges;if(!t)return null;var n=e.row;for(var r=0;r<t.length;r++){var i=t[r];if(i.start.row!=i.end.row)if(i.action[0]=="i")i.start.row<n&&(n+=i.end.row-i.start.row);else if(i.start.row<n){if(n<i.end.row||n==i.end.row&&i.start.column>0)return null;n-=i.end.row-i.start.row}}return n},this.getLineHandle=function(e){return this.$lineHandleChanges||(this.$lineHandleChanges=[]),{text:this.ace.session.getLine(e),row:e}},this.releaseLineHandles=function(){this.$lineHandleChanges=undefined},this.getLastEditEnd=function(){var e=this.ace.session.$undoManager;if(e&&e.$lastDelta)return y(e.$lastDelta.end)}}.call(m.prototype);var b=m.StringStream=function(e,t){this.pos=this.start=0,this.string=e,this.tabSize=t||8,this.lastColumnPos=this.lastColumnValue=0,this.lineStart=0};b.prototype={eol:function(){return this.pos>=this.string.length},sol:function(){return this.pos==this.lineStart},peek:function(){return this.string.charAt(this.pos)||undefined},next:function(){if(this.pos<this.string.length)return this.string.charAt(this.pos++)},eat:function(e){var t=this.string.charAt(this.pos);if(typeof e=="string")var n=t==e;else var n=t&&(e.test?e.test(t):e(t));if(n)return++this.pos,t},eatWhile:function(e){var t=this.pos;while(this.eat(e));return this.pos>t},eatSpace:function(){var e=this.pos;while(/[\s\u00a0]/.test(this.string.charAt(this.pos)))++this.pos;return this.pos>e},skipToEnd:function(){this.pos=this.string.length},skipTo:function(e){var t=this.string.indexOf(e,this.pos);if(t>-1)return this.pos=t,!0},backUp:function(e){this.pos-=e},column:function(){throw"not implemented"},indentation:function(){throw"not implemented"},match:function(e,t,n){if(typeof e!="string"){var s=this.string.slice(this.pos).match(e);return s&&s.index>0?null:(s&&t!==!1&&(this.pos+=s[0].length),s)}var r=function(e){return n?e.toLowerCase():e},i=this.string.substr(this.pos,e.length);if(r(i)==r(e))return t!==!1&&(this.pos+=e.length),!0},current:function(){return this.string.slice(this.start,this.pos)},hideFirstChars:function(e,t){this.lineStart+=e;try{return t()}finally{this.lineStart-=e}}},m.defineExtension=function(e,t){m.prototype[e]=t},o.importCssString(".normal-mode .ace_cursor{\n    border: none;\n    background-color: rgba(255,0,0,0.5);\n}\n.normal-mode .ace_hidden-cursors .ace_cursor{\n  background-color: transparent;\n  border: 1px solid red;\n  opacity: 0.7\n}\n.ace_dialog {\n  position: absolute;\n  left: 0; right: 0;\n  background: inherit;\n  z-index: 15;\n  padding: .1em .8em;\n  overflow: hidden;\n  color: inherit;\n}\n.ace_dialog-top {\n  border-bottom: 1px solid #444;\n  top: 0;\n}\n.ace_dialog-bottom {\n  border-top: 1px solid #444;\n  bottom: 0;\n}\n.ace_dialog input {\n  border: none;\n  outline: none;\n  background: transparent;\n  width: 20em;\n  color: inherit;\n  font-family: monospace;\n}","vimMode",!1),function(){function e(e,t,n){var r=e.ace.container,i;return i=r.appendChild(document.createElement("div")),n?i.className="ace_dialog ace_dialog-bottom":i.className="ace_dialog ace_dialog-top",typeof t=="string"?i.innerHTML=t:i.appendChild(t),i}function t(e,t){e.state.currentNotificationClose&&e.state.currentNotificationClose(),e.state.currentNotificationClose=t}m.defineExtension("openDialog",function(n,r,i){function a(e){if(typeof e=="string")f.value=e;else{if(o)return;if(e&&e.type=="blur"&&document.activeElement===f)return;u.state.dialog==s&&(u.state.dialog=null,u.focus()),o=!0,s.remove(),i.onClose&&i.onClose(s);var t=u;t.state.vim&&(t.state.vim.status=null,t.ace._signal("changeStatus"),t.ace.renderer.$loop.schedule(t.ace.renderer.CHANGE_CURSOR))}}if(this.virtualSelectionMode())return;i||(i={}),t(this,null);var s=e(this,n,i.bottom),o=!1,u=this;this.state.dialog=s;var f=s.getElementsByTagName("input")[0],l;if(f)i.value&&(f.value=i.value,i.selectValueOnOpen!==!1&&f.select()),i.onInput&&m.on(f,"input",function(e){i.onInput(e,f.value,a)}),i.onKeyUp&&m.on(f,"keyup",function(e){i.onKeyUp(e,f.value,a)}),m.on(f,"keydown",function(e){if(i&&i.onKeyDown&&i.onKeyDown(e,f.value,a))return;e.keyCode==13&&r(f.value);if(e.keyCode==27||i.closeOnEnter!==!1&&e.keyCode==13)m.e_stop(e),a()}),i.closeOnBlur!==!1&&m.on(f,"blur",a),f.focus();else if(l=s.getElementsByTagName("button")[0])m.on(l,"click",function(){a(),u.focus()}),i.closeOnBlur!==!1&&m.on(l,"blur",a),l.focus();return a}),m.defineExtension("openNotification",function(n,r){function a(){if(s)return;s=!0,clearTimeout(o),i.remove()}if(this.virtualSelectionMode())return;t(this,a);var i=e(this,n,r&&r.bottom),s=!1,o,u=r&&typeof r.duration!="undefined"?r.duration:5e3;return m.on(i,"click",function(e){m.e_preventDefault(e),a()}),u&&(o=setTimeout(a,u)),a})}();var w=m.Pos,S=[{keys:"<Left>",type:"keyToKey",toKeys:"h"},{keys:"<Right>",type:"keyToKey",toKeys:"l"},{keys:"<Up>",type:"keyToKey",toKeys:"k"},{keys:"<Down>",type:"keyToKey",toKeys:"j"},{keys:"g<Up>",type:"keyToKey",toKeys:"gk"},{keys:"g<Down>",type:"keyToKey",toKeys:"gj"},{keys:"<Space>",type:"keyToKey",toKeys:"l"},{keys:"<BS>",type:"keyToKey",toKeys:"h"},{keys:"<Del>",type:"keyToKey",toKeys:"x"},{keys:"<C-Space>",type:"keyToKey",toKeys:"W"},{keys:"<C-BS>",type:"keyToKey",toKeys:"B"},{keys:"<S-Space>",type:"keyToKey",toKeys:"w"},{keys:"<S-BS>",type:"keyToKey",toKeys:"b"},{keys:"<C-n>",type:"keyToKey",toKeys:"j"},{keys:"<C-p>",type:"keyToKey",toKeys:"k"},{keys:"<C-[>",type:"keyToKey",toKeys:"<Esc>"},{keys:"<C-c>",type:"keyToKey",toKeys:"<Esc>"},{keys:"<C-[>",type:"keyToKey",toKeys:"<Esc>",context:"insert"},{keys:"<C-c>",type:"keyToKey",toKeys:"<Esc>",context:"insert"},{keys:"<C-Esc>",type:"keyToKey",toKeys:"<Esc>"},{keys:"<C-Esc>",type:"keyToKey",toKeys:"<Esc>",context:"insert"},{keys:"s",type:"keyToKey",toKeys:"cl",context:"normal"},{keys:"s",type:"keyToKey",toKeys:"c",context:"visual"},{keys:"S",type:"keyToKey",toKeys:"cc",context:"normal"},{keys:"S",type:"keyToKey",toKeys:"VdO",context:"visual"},{keys:"<Home>",type:"keyToKey",toKeys:"0"},{keys:"<End>",type:"keyToKey",toKeys:"$"},{keys:"<PageUp>",type:"keyToKey",toKeys:"<C-b>"},{keys:"<PageDown>",type:"keyToKey",toKeys:"<C-f>"},{keys:"<CR>",type:"keyToKey",toKeys:"j^",context:"normal"},{keys:"<Ins>",type:"keyToKey",toKeys:"i",context:"normal"},{keys:"<Ins>",type:"action",action:"toggleOverwrite",context:"insert"},{keys:"H",type:"motion",motion:"moveToTopLine",motionArgs:{linewise:!0,toJumplist:!0}},{keys:"M",type:"motion",motion:"moveToMiddleLine",motionArgs:{linewise:!0,toJumplist:!0}},{keys:"L",type:"motion",motion:"moveToBottomLine",motionArgs:{linewise:!0,toJumplist:!0}},{keys:"h",type:"motion",motion:"moveByCharacters",motionArgs:{forward:!1}},{keys:"l",type:"motion",motion:"moveByCharacters",motionArgs:{forward:!0}},{keys:"j",type:"motion",motion:"moveByLines",motionArgs:{forward:!0,linewise:!0}},{keys:"k",type:"motion",motion:"moveByLines",motionArgs:{forward:!1,linewise:!0}},{keys:"gj",type:"motion",motion:"moveByDisplayLines",motionArgs:{forward:!0}},{keys:"gk",type:"motion",motion:"moveByDisplayLines",motionArgs:{forward:!1}},{keys:"w",type:"motion",motion:"moveByWords",motionArgs:{forward:!0,wordEnd:!1}},{keys:"W",type:"motion",motion:"moveByWords",motionArgs:{forward:!0,wordEnd:!1,bigWord:!0}},{keys:"e",type:"motion",motion:"moveByWords",motionArgs:{forward:!0,wordEnd:!0,inclusive:!0}},{keys:"E",type:"motion",motion:"moveByWords",motionArgs:{forward:!0,wordEnd:!0,bigWord:!0,inclusive:!0}},{keys:"b",type:"motion",motion:"moveByWords",motionArgs:{forward:!1,wordEnd:!1}},{keys:"B",type:"motion",motion:"moveByWords",motionArgs:{forward:!1,wordEnd:!1,bigWord:!0}},{keys:"ge",type:"motion",motion:"moveByWords",motionArgs:{forward:!1,wordEnd:!0,inclusive:!0}},{keys:"gE",type:"motion",motion:"moveByWords",motionArgs:{forward:!1,wordEnd:!0,bigWord:!0,inclusive:!0}},{keys:"{",type:"motion",motion:"moveByParagraph",motionArgs:{forward:!1,toJumplist:!0}},{keys:"}",type:"motion",motion:"moveByParagraph",motionArgs:{forward:!0,toJumplist:!0}},{keys:"(",type:"motion",motion:"moveBySentence",motionArgs:{forward:!1}},{keys:")",type:"motion",motion:"moveBySentence",motionArgs:{forward:!0}},{keys:"<C-f>",type:"motion",motion:"moveByPage",motionArgs:{forward:!0}},{keys:"<C-b>",type:"motion",motion:"moveByPage",motionArgs:{forward:!1}},{keys:"<C-d>",type:"motion",motion:"moveByScroll",motionArgs:{forward:!0,explicitRepeat:!0}},{keys:"<C-u>",type:"motion",motion:"moveByScroll",motionArgs:{forward:!1,explicitRepeat:!0}},{keys:"gg",type:"motion",motion:"moveToLineOrEdgeOfDocument",motionArgs:{forward:!1,explicitRepeat:!0,linewise:!0,toJumplist:!0}},{keys:"G",type:"motion",motion:"moveToLineOrEdgeOfDocument",motionArgs:{forward:!0,explicitRepeat:!0,linewise:!0,toJumplist:!0}},{keys:"g$",type:"motion",motion:"moveToEndOfDisplayLine"},{keys:"g^",type:"motion",motion:"moveToStartOfDisplayLine"},{keys:"g0",type:"motion",motion:"moveToStartOfDisplayLine"},{keys:"0",type:"motion",motion:"moveToStartOfLine"},{keys:"^",type:"motion",motion:"moveToFirstNonWhiteSpaceCharacter"},{keys:"+",type:"motion",motion:"moveByLines",motionArgs:{forward:!0,toFirstChar:!0}},{keys:"-",type:"motion",motion:"moveByLines",motionArgs:{forward:!1,toFirstChar:!0}},{keys:"_",type:"motion",motion:"moveByLines",motionArgs:{forward:!0,toFirstChar:!0,repeatOffset:-1}},{keys:"$",type:"motion",motion:"moveToEol",motionArgs:{inclusive:!0}},{keys:"%",type:"motion",motion:"moveToMatchedSymbol",motionArgs:{inclusive:!0,toJumplist:!0}},{keys:"f<character>",type:"motion",motion:"moveToCharacter",motionArgs:{forward:!0,inclusive:!0}},{keys:"F<character>",type:"motion",motion:"moveToCharacter",motionArgs:{forward:!1}},{keys:"t<character>",type:"motion",motion:"moveTillCharacter",motionArgs:{forward:!0,inclusive:!0}},{keys:"T<character>",type:"motion",motion:"moveTillCharacter",motionArgs:{forward:!1}},{keys:";",type:"motion",motion:"repeatLastCharacterSearch",motionArgs:{forward:!0}},{keys:",",type:"motion",motion:"repeatLastCharacterSearch",motionArgs:{forward:!1}},{keys:"'<register>",type:"motion",motion:"goToMark",motionArgs:{toJumplist:!0,linewise:!0}},{keys:"`<register>",type:"motion",motion:"goToMark",motionArgs:{toJumplist:!0}},{keys:"]`",type:"motion",motion:"jumpToMark",motionArgs:{forward:!0}},{keys:"[`",type:"motion",motion:"jumpToMark",motionArgs:{forward:!1}},{keys:"]'",type:"motion",motion:"jumpToMark",motionArgs:{forward:!0,linewise:!0}},{keys:"['",type:"motion",motion:"jumpToMark",motionArgs:{forward:!1,linewise:!0}},{keys:"]p",type:"action",action:"paste",isEdit:!0,actionArgs:{after:!0,isEdit:!0,matchIndent:!0}},{keys:"[p",type:"action",action:"paste",isEdit:!0,actionArgs:{after:!1,isEdit:!0,matchIndent:!0}},{keys:"]<character>",type:"motion",motion:"moveToSymbol",motionArgs:{forward:!0,toJumplist:!0}},{keys:"[<character>",type:"motion",motion:"moveToSymbol",motionArgs:{forward:!1,toJumplist:!0}},{keys:"|",type:"motion",motion:"moveToColumn"},{keys:"o",type:"motion",motion:"moveToOtherHighlightedEnd",context:"visual"},{keys:"O",type:"motion",motion:"moveToOtherHighlightedEnd",motionArgs:{sameLine:!0},context:"visual"},{keys:"d",type:"operator",operator:"delete"},{keys:"y",type:"operator",operator:"yank"},{keys:"c",type:"operator",operator:"change"},{keys:"=",type:"operator",operator:"indentAuto"},{keys:">",type:"operator",operator:"indent",operatorArgs:{indentRight:!0}},{keys:"<",type:"operator",operator:"indent",operatorArgs:{indentRight:!1}},{keys:"g~",type:"operator",operator:"changeCase"},{keys:"gu",type:"operator",operator:"changeCase",operatorArgs:{toLower:!0},isEdit:!0},{keys:"gU",type:"operator",operator:"changeCase",operatorArgs:{toLower:!1},isEdit:!0},{keys:"n",type:"motion",motion:"findNext",motionArgs:{forward:!0,toJumplist:!0}},{keys:"N",type:"motion",motion:"findNext",motionArgs:{forward:!1,toJumplist:!0}},{keys:"gn",type:"motion",motion:"findAndSelectNextInclusive",motionArgs:{forward:!0}},{keys:"gN",type:"motion",motion:"findAndSelectNextInclusive",motionArgs:{forward:!1}},{keys:"gq",type:"operator",operator:"hardWrap"},{keys:"gw",type:"operator",operator:"hardWrap",operatorArgs:{keepCursor:!0}},{keys:"x",type:"operatorMotion",operator:"delete",motion:"moveByCharacters",motionArgs:{forward:!0},operatorMotionArgs:{visualLine:!1}},{keys:"X",type:"operatorMotion",operator:"delete",motion:"moveByCharacters",motionArgs:{forward:!1},operatorMotionArgs:{visualLine:!0}},{keys:"D",type:"operatorMotion",operator:"delete",motion:"moveToEol",motionArgs:{inclusive:!0},context:"normal"},{keys:"D",type:"operator",operator:"delete",operatorArgs:{linewise:!0},context:"visual"},{keys:"Y",type:"operatorMotion",operator:"yank",motion:"expandToLine",motionArgs:{linewise:!0},context:"normal"},{keys:"Y",type:"operator",operator:"yank",operatorArgs:{linewise:!0},context:"visual"},{keys:"C",type:"operatorMotion",operator:"change",motion:"moveToEol",motionArgs:{inclusive:!0},context:"normal"},{keys:"C",type:"operator",operator:"change",operatorArgs:{linewise:!0},context:"visual"},{keys:"~",type:"operatorMotion",operator:"changeCase",motion:"moveByCharacters",motionArgs:{forward:!0},operatorArgs:{shouldMoveCursor:!0},context:"normal"},{keys:"~",type:"operator",operator:"changeCase",context:"visual"},{keys:"<C-u>",type:"operatorMotion",operator:"delete",motion:"moveToStartOfLine",context:"insert"},{keys:"<C-w>",type:"operatorMotion",operator:"delete",motion:"moveByWords",motionArgs:{forward:!1,wordEnd:!1},context:"insert"},{keys:"<C-w>",type:"idle",context:"normal"},{keys:"<C-i>",type:"action",action:"jumpListWalk",actionArgs:{forward:!0}},{keys:"<C-o>",type:"action",action:"jumpListWalk",actionArgs:{forward:!1}},{keys:"<C-e>",type:"action",action:"scroll",actionArgs:{forward:!0,linewise:!0}},{keys:"<C-y>",type:"action",action:"scroll",actionArgs:{forward:!1,linewise:!0}},{keys:"a",type:"action",action:"enterInsertMode",isEdit:!0,actionArgs:{insertAt:"charAfter"},context:"normal"},{keys:"A",type:"action",action:"enterInsertMode",isEdit:!0,actionArgs:{insertAt:"eol"},context:"normal"},{keys:"A",type:"action",action:"enterInsertMode",isEdit:!0,actionArgs:{insertAt:"endOfSelectedArea"},context:"visual"},{keys:"i",type:"action",action:"enterInsertMode",isEdit:!0,actionArgs:{insertAt:"inplace"},context:"normal"},{keys:"gi",type:"action",action:"enterInsertMode",isEdit:!0,actionArgs:{insertAt:"lastEdit"},context:"normal"},{keys:"I",type:"action",action:"enterInsertMode",isEdit:!0,actionArgs:{insertAt:"firstNonBlank"},context:"normal"},{keys:"gI",type:"action",action:"enterInsertMode",isEdit:!0,actionArgs:{insertAt:"bol"},context:"normal"},{keys:"I",type:"action",action:"enterInsertMode",isEdit:!0,actionArgs:{insertAt:"startOfSelectedArea"},context:"visual"},{keys:"o",type:"action",action:"newLineAndEnterInsertMode",isEdit:!0,interlaceInsertRepeat:!0,actionArgs:{after:!0},context:"normal"},{keys:"O",type:"action",action:"newLineAndEnterInsertMode",isEdit:!0,interlaceInsertRepeat:!0,actionArgs:{after:!1},context:"normal"},{keys:"v",type:"action",action:"toggleVisualMode"},{keys:"V",type:"action",action:"toggleVisualMode",actionArgs:{linewise:!0}},{keys:"<C-v>",type:"action",action:"toggleVisualMode",actionArgs:{blockwise:!0}},{keys:"<C-q>",type:"action",action:"toggleVisualMode",actionArgs:{blockwise:!0}},{keys:"gv",type:"action",action:"reselectLastSelection"},{keys:"J",type:"action",action:"joinLines",isEdit:!0},{keys:"gJ",type:"action",action:"joinLines",actionArgs:{keepSpaces:!0},isEdit:!0},{keys:"p",type:"action",action:"paste",isEdit:!0,actionArgs:{after:!0,isEdit:!0}},{keys:"P",type:"action",action:"paste",isEdit:!0,actionArgs:{after:!1,isEdit:!0}},{keys:"r<character>",type:"action",action:"replace",isEdit:!0},{keys:"@<register>",type:"action",action:"replayMacro"},{keys:"q<register>",type:"action",action:"enterMacroRecordMode"},{keys:"R",type:"action",action:"enterInsertMode",isEdit:!0,actionArgs:{replace:!0},context:"normal"},{keys:"R",type:"operator",operator:"change",operatorArgs:{linewise:!0,fullLine:!0},context:"visual",exitVisualBlock:!0},{keys:"u",type:"action",action:"undo",context:"normal"},{keys:"u",type:"operator",operator:"changeCase",operatorArgs:{toLower:!0},context:"visual",isEdit:!0},{keys:"U",type:"operator",operator:"changeCase",operatorArgs:{toLower:!1},context:"visual",isEdit:!0},{keys:"<C-r>",type:"action",action:"redo"},{keys:"m<register>",type:"action",action:"setMark"},{keys:'"<register>',type:"action",action:"setRegister"},{keys:"<C-r><register>",type:"action",action:"insertRegister",context:"insert",isEdit:!0},{keys:"<C-o>",type:"action",action:"oneNormalCommand",context:"insert"},{keys:"zz",type:"action",action:"scrollToCursor",actionArgs:{position:"center"}},{keys:"z.",type:"action",action:"scrollToCursor",actionArgs:{position:"center"},motion:"moveToFirstNonWhiteSpaceCharacter"},{keys:"zt",type:"action",action:"scrollToCursor",actionArgs:{position:"top"}},{keys:"z<CR>",type:"action",action:"scrollToCursor",actionArgs:{position:"top"},motion:"moveToFirstNonWhiteSpaceCharacter"},{keys:"zb",type:"action",action:"scrollToCursor",actionArgs:{position:"bottom"}},{keys:"z-",type:"action",action:"scrollToCursor",actionArgs:{position:"bottom"},motion:"moveToFirstNonWhiteSpaceCharacter"},{keys:".",type:"action",action:"repeatLastEdit"},{keys:"<C-a>",type:"action",action:"incrementNumberToken",isEdit:!0,actionArgs:{increase:!0,backtrack:!1}},{keys:"<C-x>",type:"action",action:"incrementNumberToken",isEdit:!0,actionArgs:{increase:!1,backtrack:!1}},{keys:"<C-t>",type:"action",action:"indent",actionArgs:{indentRight:!0},context:"insert"},{keys:"<C-d>",type:"action",action:"indent",actionArgs:{indentRight:!1},context:"insert"},{keys:"a<register>",type:"motion",motion:"textObjectManipulation"},{keys:"i<register>",type:"motion",motion:"textObjectManipulation",motionArgs:{textObjectInner:!0}},{keys:"/",type:"search",searchArgs:{forward:!0,querySrc:"prompt",toJumplist:!0}},{keys:"?",type:"search",searchArgs:{forward:!1,querySrc:"prompt",toJumplist:!0}},{keys:"*",type:"search",searchArgs:{forward:!0,querySrc:"wordUnderCursor",wholeWordOnly:!0,toJumplist:!0}},{keys:"#",type:"search",searchArgs:{forward:!1,querySrc:"wordUnderCursor",wholeWordOnly:!0,toJumplist:!0}},{keys:"g*",type:"search",searchArgs:{forward:!0,querySrc:"wordUnderCursor",toJumplist:!0}},{keys:"g#",type:"search",searchArgs:{forward:!1,querySrc:"wordUnderCursor",toJumplist:!0}},{keys:":",type:"ex"}],x=S.length,T=[{name:"colorscheme",shortName:"colo"},{name:"map"},{name:"imap",shortName:"im"},{name:"nmap",shortName:"nm"},{name:"vmap",shortName:"vm"},{name:"omap",shortName:"om"},{name:"noremap",shortName:"no"},{name:"nnoremap",shortName:"nn"},{name:"vnoremap",shortName:"vn"},{name:"inoremap",shortName:"ino"},{name:"onoremap",shortName:"ono"},{name:"unmap"},{name:"mapclear",shortName:"mapc"},{name:"nmapclear",shortName:"nmapc"},{name:"vmapclear",shortName:"vmapc"},{name:"imapclear",shortName:"imapc"},{name:"omapclear",shortName:"omapc"},{name:"write",shortName:"w"},{name:"undo",shortName:"u"},{name:"redo",shortName:"red"},{name:"set",shortName:"se"},{name:"setlocal",shortName:"setl"},{name:"setglobal",shortName:"setg"},{name:"sort",shortName:"sor"},{name:"substitute",shortName:"s",possiblyAsync:!0},{name:"startinsert",shortName:"start"},{name:"nohlsearch",shortName:"noh"},{name:"yank",shortName:"y"},{name:"delmarks",shortName:"delm"},{name:"registers",shortName:"reg",excludeFromCommandHistory:!0},{name:"vglobal",shortName:"v"},{name:"delete",shortName:"d"},{name:"join",shortName:"j"},{name:"normal",shortName:"norm"},{name:"global",shortName:"g"}],N=pt(""),A=/[\d]/,O=[m.isWordChar,function(e){return e&&!m.isWordChar(e)&&!/\s/.test(e)}],M=[function(e){return/\S/.test(e)}],_=["<",">"],D=["-",'"',".",":","_","/","+"],P=/^\w$/,H;try{H=new RegExp("^[\\p{Lu}]$","u")}catch(B){H=/^[A-Z]$/}var X={};V("filetype",undefined,"string",["ft"],function(e,t){if(t===undefined)return;if(e===undefined){var n=t.getOption("mode");return n=="null"?"":n}var n=e==""?"null":e;t.setOption("mode",n)}),V("textwidth",80,"number",["tw"],function(e,t){if(t===undefined)return;if(e===undefined){var n=t.getOption("textwidth");return n}var r=Math.round(e);r>1&&t.setOption("textwidth",r)});var K=function(){function s(s,o,u){function l(n){var r=++t%e,o=i[r];o&&o.clear(),i[r]=s.setBookmark(n)}var a=t%e,f=i[a];if(f){var c=f.find();c&&!jt(c,o)&&l(o)}else l(o);l(u),n=t,r=t-e+1,r<0&&(r=0)}function o(s,o){t+=o,t>n?t=n:t<r&&(t=r);var u=i[(e+t)%e];if(u&&!u.find()){var a=o>0?1:-1,f,l=s.getCursor();do{t+=a,u=i[(e+t)%e];if(u&&(f=u.find())&&!jt(l,f))break}while(t<n&&t>r)}return u}function u(e,n){var r=t,i=o(e,n);return t=r,i&&i.find()}var e=100,t=-1,n=0,r=0,i=new Array(e);return{cachedCursor:undefined,add:s,find:u,move:o}},Q=function(e){return e?{changes:e.changes,expectCursorActivityForChange:e.expectCursorActivityForChange}:{changes:[],expectCursorActivityForChange:!1}};G.prototype={exitMacroRecordMode:function(){var e=Z.macroModeState;e.onRecordingDone&&e.onRecordingDone(),e.onRecordingDone=undefined,e.isRecording=!1},enterMacroRecordMode:function(e,t){var n=Z.registerController.getRegister(t);if(n){n.clear(),this.latestRegister=t;if(e.openDialog){var r=jn("span",{"class":"cm-vim-message"},"recording @"+t);this.onRecordingDone=e.openDialog(r,null,{bottom:!0})}this.isRecording=!0}}};var Z,tt,nt={enterVimMode:C,leaveVimMode:k,buildKeyMap:function(){},getRegisterController:function(){return Z.registerController},resetVimGlobalState_:et,getVimGlobalState_:function(){return Z},maybeInitVimState_:Y,suppressErrorLogging:!1,InsertModeKey:pr,map:function(e,t,n){tr.map(e,t,n)},unmap:function(e,t){return tr.unmap(e,t)},noremap:function(e,t,n){tr.map(e,t,n,!0)},mapclear:function(e){var t=S.length,n=x,r=S.slice(0,t-n);S=S.slice(t-n);if(e)for(var i=r.length-1;i>=0;i--){var s=r[i];if(e!==s.context)if(s.context)this._mapCommand(s);else{var o=["normal","insert","visual"];for(var u in o)if(o[u]!==e){var a={};for(var f in s)a[f]=s[f];a.context=o[u],this._mapCommand(a)}}}},langmap:ht,vimKeyFromEvent:ct,setOption:$,getOption:J,defineOption:V,defineEx:function(e,t,n){if(!t)t=e;else if(e.indexOf(t)!==0)throw new Error('(Vim.defineEx) "'+t+'" is not a prefix of "'+e+'", command not registered');er[e]=n,tr.commandMap_[t]={name:e,shortName:t,type:"api"}},handleKey:function(e,t,n){var r=this.findKey(e,t,n);if(typeof r=="function")return r()},multiSelectHandleKey:Sr,findKey:function(e,t,n){function i(){var r=Z.macroModeState;if(r.isRecording){if(t=="q")return r.exitMacroRecordMode(),vt(e),!0;n!="mapping"&&ur(r,t)}}function s(){if(t=="<Esc>"){if(r.visualMode)tn(e);else{if(!r.insertMode)return;rr(e)}return vt(e),!0}}function o(){if(s())return!0;r.inputState.keyBuffer.push(t);var n=r.inputState.keyBuffer.join(""),i=t.length==1,o=Et.matchCommand(n,S,r.inputState,"insert"),u=r.inputState.changeQueue;if(o.type=="none")return vt(e),!1;if(o.type=="partial"){o.expectLiteralNext&&(r.expectLiteralNext=!0),tt&&window.clearTimeout(tt),tt=i&&window.setTimeout(function(){r.insertMode&&r.inputState.keyBuffer.length&&vt(e)},J("insertModeEscKeysTimeout"));if(i){var a=e.listSelections();if(!u||u.removed.length!=a.length)u=r.inputState.changeQueue=new mt;u.inserted+=t;for(var f=0;f<a.length;f++){var l=It(a[f].anchor,a[f].head),c=qt(a[f].anchor,a[f].head),h=e.getRange(l,e.state.overwrite?Mt(c,0,1):c);u.removed[f]=(u.removed[f]||"")+h}}return!i}r.expectLiteralNext=!1,tt&&window.clearTimeout(tt);if(o.command&&u){var a=e.listSelections();for(var f=0;f<a.length;f++){var p=a[f].head;e.replaceRange(u.removed[f]||"",Mt(p,0,-u.inserted.length),p,"+input")}Z.macroModeState.lastInsertModeChanges.changes.pop()}return o.command||vt(e),o.command}function u(){if(i()||s())return!0;r.inputState.keyBuffer.push(t);var n=r.inputState.keyBuffer.join("");if(/^[1-9]\d*$/.test(n))return!0;var o=/^(\d*)(.*)$/.exec(n);if(!o)return vt(e),!1;var u=r.visualMode?"visual":"normal",a=o[2]||o[1];r.inputState.operatorShortcut&&r.inputState.operatorShortcut.slice(-1)==a&&(a=r.inputState.operatorShortcut);var f=Et.matchCommand(a,S,r.inputState,u);return f.type=="none"?(vt(e),!1):f.type=="partial"?(f.expectLiteralNext&&(r.expectLiteralNext=!0),!0):f.type=="clear"?(vt(e),!0):(r.expectLiteralNext=!1,r.inputState.keyBuffer.length=0,o=/^(\d*)(.*)$/.exec(n),o[1]&&o[1]!="0"&&r.inputState.pushRepeatDigit(o[1]),f.command)}var r=Y(e),a;return r.insertMode?a=o():a=u(),a===!1?!r.insertMode&&t.length===1?function(){return!0}:undefined:a===!0?function(){return!0}:function(){if((a.operator||a.isEdit)&&e.getOption("readOnly"))return;return e.operation(function(){e.curOp.isVimOp=!0;try{a.type=="keyToKey"?ut(e,a.toKeys,a):Et.processCommand(e,r,a)}catch(t){throw e.state.vim=undefined,Y(e),nt.suppressErrorLogging||console.log(t),t}return!0})}},handleEx:function(e,t){tr.processCommand(e,t)},defineMotion:xt,defineAction:Lt,defineOperator:Ct,mapCommand:sr,_mapCommand:ir,defineRegister:yt,exitVisualMode:tn,exitInsertMode:rr},rt=[],it=!1,st,at={Return:"CR",Backspace:"BS",Delete:"Del",Escape:"Esc",Insert:"Ins",ArrowLeft:"Left",ArrowRight:"Right",ArrowUp:"Up",ArrowDown:"Down",Enter:"CR"," ":"Space"},ft={Shift:1,Alt:1,Command:1,Control:1,CapsLock:1,AltGraph:1,Dead:1,Unidentified:1},lt={};"Left|Right|Up|Down|End|Home".split("|").concat(Object.keys(at)).forEach(function(e){lt[(at[e]||"").toLowerCase()]=lt[e.toLowerCase()]=e}),V("langmap",undefined,"string",["lmap"],function(e,t){if(e===undefined)return N.string;ht(e)}),dt.prototype.pushRepeatDigit=function(e){this.operator?this.motionRepeat=this.motionRepeat.concat(e):this.prefixRepeat=this.prefixRepeat.concat(e)},dt.prototype.getRepeat=function(){var e=0;if(this.prefixRepeat.length>0||this.motionRepeat.length>0)e=1,this.prefixRepeat.length>0&&(e*=parseInt(this.prefixRepeat.join(""),10)),this.motionRepeat.length>0&&(e*=parseInt(this.motionRepeat.join(""),10));return e},gt.prototype={setText:function(e,t,n){this.keyBuffer=[e||""],this.linewise=!!t,this.blockwise=!!n},pushText:function(e,t){t&&(this.linewise||this.keyBuffer.push("\n"),this.linewise=!0),this.keyBuffer.push(e)},pushInsertModeChanges:function(e){this.insertModeChanges.push(Q(e))},pushSearchQuery:function(e){this.searchQueries.push(e)},clear:function(){this.keyBuffer=[],this.insertModeChanges=[],this.searchQueries=[],this.linewise=!1},toString:function(){return this.keyBuffer.join("")}},bt.prototype={pushText:function(e,t,n,r,i){if(e==="_")return;r&&n.charAt(n.length-1)!=="\n"&&(n+="\n");var s=this.isValidRegister(e)?this.getRegister(e):null;if(!s){switch(t){case"yank":this.registers[0]=new gt(n,r,i);break;case"delete":case"change":n.indexOf("\n")==-1?this.registers["-"]=new gt(n,r):(this.shiftNumericRegisters_(),this.registers[1]=new gt(n,r))}this.unnamedRegister.setText(n,r,i);return}var o=R(e);o?s.pushText(n,r):s.setText(n,r,i),e==="+"&&typeof navigator!="undefined"&&typeof navigator.clipboard!="undefined"&&typeof navigator.clipboard.readText=="function"&&navigator.clipboard.writeText(n),this.unnamedRegister.setText(s.toString(),r)},getRegister:function(e){return this.isValidRegister(e)?(e=e.toLowerCase(),this.registers[e]||(this.registers[e]=new gt),this.registers[e]):this.unnamedRegister},isValidRegister:function(e){return e&&(W(e,D)||P.test(e))},shiftNumericRegisters_:function(){for(var e=9;e>=2;e--)this.registers[e]=this.getRegister(""+(e-1))}},wt.prototype={nextMatch:function(e,t){var n=this.historyBuffer,r=t?-1:1;this.initialPrefix===null&&(this.initialPrefix=e);for(var i=this.iterator+r;t?i>=0:i<n.length;i+=r){var s=n[i];for(var o=0;o<=s.length;o++)if(this.initialPrefix==s.substring(0,o))return this.iterator=i,s}if(i>=n.length)return this.iterator=n.length,this.initialPrefix;if(i<0)return e},pushInput:function(e){var t=this.historyBuffer.indexOf(e);t>-1&&this.historyBuffer.splice(t,1),e.length&&this.historyBuffer.push(e)},reset:function(){this.initialPrefix=null,this.iterator=this.historyBuffer.length}};var Et={matchCommand:function(e,t,n,r){var i=_t(e,t,r,n);if(!i.full&&!i.partial)return{type:"none"};if(!i.full&&i.partial)return{type:"partial",expectLiteralNext:i.partial.length==1&&i.partial[0].keys.slice(-11)=="<character>"};var s;for(var o=0;o<i.full.length;o++){var u=i.full[o];s||(s=u)}if(s.keys.slice(-11)=="<character>"||s.keys.slice(-10)=="<register>"){var a=Pt(e);if(!a||a.length>1)return{type:"clear"};n.selectedCharacter=a}return{type:"full",command:s}},processCommand:function(e,t,n){t.inputState.repeatOverride=n.repeatOverride;switch(n.type){case"motion":this.processMotion(e,t,n);break;case"operator":this.processOperator(e,t,n);break;case"operatorMotion":this.processOperatorMotion(e,t,n);break;case"action":this.processAction(e,t,n);break;case"search":this.processSearch(e,t,n);break;case"ex":case"keyToEx":this.processEx(e,t,n);break;default:}},processMotion:function(e,t,n){t.inputState.motion=n.motion,t.inputState.motionArgs=Ot(n.motionArgs),this.evalInput(e,t)},processOperator:function(e,t,n){var r=t.inputState;if(r.operator){if(r.operator==n.operator){r.motion="expandToLine",r.motionArgs={linewise:!0},this.evalInput(e,t);return}vt(e)}r.operator=n.operator,r.operatorArgs=Ot(n.operatorArgs),n.keys.length>1&&(r.operatorShortcut=n.keys),n.exitVisualBlock&&(t.visualBlock=!1,Yt(e)),t.visualMode&&this.evalInput(e,t)},processOperatorMotion:function(e,t,n){var r=t.visualMode,i=Ot(n.operatorMotionArgs);i&&r&&i.visualLine&&(t.visualLine=!0),this.processOperator(e,t,n),r||this.processMotion(e,t,n)},processAction:function(e,t,n){var r=t.inputState,i=r.getRepeat(),s=!!i,o=Ot(n.actionArgs)||{};r.selectedCharacter&&(o.selectedCharacter=r.selectedCharacter),n.operator&&this.processOperator(e,t,n),n.motion&&this.processMotion(e,t,n),(n.motion||n.operator)&&this.evalInput(e,t),o.repeat=i||1,o.repeatIsExplicit=s,o.registerName=r.registerName,vt(e),t.lastMotion=null,n.isEdit&&this.recordLastEdit(t,r,n),kt[n.action](e,o,t)},processSearch:function(e,t,n){function a(r,i,s){Z.searchHistoryController.pushInput(r),Z.searchHistoryController.reset();try{Un(e,r,i,s)}catch(o){Fn(e,"Invalid regex: "+r),vt(e);return}Et.processMotion(e,t,{type:"motion",motion:"findNext",motionArgs:{forward:!0,toJumplist:n.searchArgs.toJumplist}})}function f(e){a(e,!0,!0);var t=Z.macroModeState;t.isRecording&&fr(t,e)}function l(t,n,i){var s=ct(t),o,a;s=="<Up>"||s=="<Down>"?(o=s=="<Up>"?!0:!1,a=t.target?t.target.selectionEnd:0,n=Z.searchHistoryController.nextMatch(n,o)||"",i(n),a&&t.target&&(t.target.selectionEnd=t.target.selectionStart=Math.min(a,t.target.value.length))):s&&s!="<Left>"&&s!="<Right>"&&Z.searchHistoryController.reset();var f;try{f=Un(e,n,!0,!0)}catch(t){}f?e.scrollIntoView(Vn(e,!r,f),30):(Jn(e),e.scrollTo(u.left,u.top))}function c(t,n,r){var i=ct(t);i=="<Esc>"||i=="<C-c>"||i=="<C-[>"||i=="<BS>"&&n==""?(Z.searchHistoryController.pushInput(n),Z.searchHistoryController.reset(),Un(e,o),Jn(e),e.scrollTo(u.left,u.top),m.e_stop(t),vt(e),r(),e.focus()):i=="<Up>"||i=="<Down>"?m.e_stop(t):i=="<C-u>"&&(m.e_stop(t),r(""))}if(!e.getSearchCursor)return;var r=n.searchArgs.forward,i=n.searchArgs.wholeWordOnly;Cn(e).setReversed(!r);var s=r?"/":"?",o=Cn(e).getQuery(),u=e.getScrollInfo();switch(n.searchArgs.querySrc){case"prompt":var h=Z.macroModeState;if(h.isPlaying){var p=h.replaySearchQueries.shift();a(p,!0,!1)}else qn(e,{onClose:f,prefix:s,desc:"(JavaScript regexp)",onKeyUp:l,onKeyDown:c});break;case"wordUnderCursor":var d=on(e,{noSymbol:!0}),v=!0;d||(d=on(e,{noSymbol:!1}),v=!1);if(!d){Fn(e,"No word under cursor"),vt(e);return}var p=e.getLine(d.start.line).substring(d.start.ch,d.end.ch);v&&i?p="\\b"+p+"\\b":p=Wt(p),Z.jumpList.cachedCursor=e.getCursor(),e.setCursor(d.start),a(p,!0,!1)}},processEx:function(e,t,n){function r(t){Z.exCommandHistoryController.pushInput(t),Z.exCommandHistoryController.reset(),tr.processCommand(e,t),e.state.vim&&vt(e)}function i(t,n,r){var i=ct(t),s,o;if(i=="<Esc>"||i=="<C-c>"||i=="<C-[>"||i=="<BS>"&&n=="")Z.exCommandHistoryController.pushInput(n),Z.exCommandHistoryController.reset(),m.e_stop(t),vt(e),r(),e.focus();i=="<Up>"||i=="<Down>"?(m.e_stop(t),s=i=="<Up>"?!0:!1,o=t.target?t.target.selectionEnd:0,n=Z.exCommandHistoryController.nextMatch(n,s)||"",r(n),o&&t.target&&(t.target.selectionEnd=t.target.selectionStart=Math.min(o,t.target.value.length))):i=="<C-u>"?(m.e_stop(t),r("")):i&&i!="<Left>"&&i!="<Right>"&&Z.exCommandHistoryController.reset()}n.type=="keyToEx"?tr.processCommand(e,n.exArgs.input):t.visualMode?qn(e,{onClose:r,prefix:":",value:"'<,'>",onKeyDown:i,selectValueOnOpen:!1}):qn(e,{onClose:r,prefix:":",onKeyDown:i})},evalInput:function(e,t){var n=t.inputState,r=n.motion,i=n.motionArgs||{},s=n.operator,o=n.operatorArgs||{},u=n.registerName,a=t.sel,f=Bt(t.visualMode?At(e,a.head):e.getCursor("head")),l=Bt(t.visualMode?At(e,a.anchor):e.getCursor("anchor")),c=Bt(f),h=Bt(l),p,d,v;s&&this.recordLastEdit(t,n),n.repeatOverride!==undefined?v=n.repeatOverride:v=n.getRepeat();if(v>0&&i.explicitRepeat)i.repeatIsExplicit=!0;else if(i.noRepeat||!i.explicitRepeat&&v===0)v=1,i.repeatIsExplicit=!1;n.selectedCharacter&&(i.selectedCharacter=o.selectedCharacter=n.selectedCharacter),i.repeat=v,vt(e);if(r){var m=St[r](e,f,i,t,n);t.lastMotion=St[r];if(!m)return;if(i.toJumplist){!s&&e.ace.curOp!=null&&(e.ace.curOp.command.scrollIntoView="center-animate");var g=Z.jumpList,y=g.cachedCursor;y?(an(e,y,m),delete g.cachedCursor):an(e,f,m)}m instanceof Array?(d=m[0],p=m[1]):p=m,p||(p=Bt(f));if(t.visualMode){if(!t.visualBlock||p.ch!==Infinity)p=At(e,p,c);d&&(d=At(e,d)),d=d||h,a.anchor=d,a.head=p,Yt(e),yn(e,t,"<",Ft(d,p)?d:p),yn(e,t,">",Ft(d,p)?p:d)}else s||(e.ace.curOp&&(e.ace.curOp.vimDialogScroll="center-animate"),p=At(e,p,c),e.setCursor(p.line,p.ch))}if(s){if(o.lastSel){d=h;var b=o.lastSel,S=Math.abs(b.head.line-b.anchor.line),x=Math.abs(b.head.ch-b.anchor.ch);b.visualLine?p=new w(h.line+S,h.ch):b.visualBlock?p=new w(h.line+S,h.ch+x):b.head.line==b.anchor.line?p=new w(h.line,h.ch+x):p=new w(h.line+S,h.ch),t.visualMode=!0,t.visualLine=b.visualLine,t.visualBlock=b.visualBlock,a=t.sel={anchor:d,head:p},Yt(e)}else t.visualMode&&(o.lastSel={anchor:Bt(a.anchor),head:Bt(a.head),visualBlock:t.visualBlock,visualLine:t.visualLine});var T,N,C,k,L;if(t.visualMode){T=It(a.head,a.anchor),N=qt(a.head,a.anchor),C=t.visualLine||o.linewise,k=t.visualBlock?"block":C?"line":"char";var A=E(e,T,N);L=Zt(e,{anchor:A.start,head:A.end},k);if(C){var O=L.ranges;if(k=="block")for(var M=0;M<O.length;M++)O[M].head.ch=Ut(e,O[M].head.line);else k=="line"&&(O[0].head=new w(O[0].head.line+1,0))}}else{T=Bt(d||h),N=Bt(p||c);if(Ft(N,T)){var _=T;T=N,N=_}C=i.linewise||o.linewise,C?rn(e,T,N):i.forward&&nn(e,T,N),k="char";var D=!i.inclusive||C,A=E(e,T,N);L=Zt(e,{anchor:A.start,head:A.end},k,D)}e.setSelections(L.ranges,L.primary),t.lastMotion=null,o.repeat=v,o.registerName=u,o.linewise=C;var P=Nt[s](e,o,L.ranges,h,p);t.visualMode&&tn(e,P!=null),P&&e.setCursor(P)}},recordLastEdit:function(e,t,n){var r=Z.macroModeState;if(r.isPlaying)return;e.lastEditInputState=t,e.lastEditActionCommand=n,r.lastInsertModeChanges.changes=[],r.lastInsertModeChanges.expectCursorActivityForChange=!1,r.lastInsertModeChanges.visualBlock=e.visualBlock?e.sel.head.line-e.sel.anchor.line:0}},St={moveToTopLine:function(e,t,n){var r=Qn(e).top+n.repeat-1;return new w(r,sn(e.getLine(r)))},moveToMiddleLine:function(e){var t=Qn(e),n=Math.floor((t.top+t.bottom)*.5);return new w(n,sn(e.getLine(n)))},moveToBottomLine:function(e,t,n){var r=Qn(e).bottom-n.repeat+1;return new w(r,sn(e.getLine(r)))},expandToLine:function(e,t,n){var r=t;return new w(r.line+n.repeat-1,Infinity)},findNext:function(e,t,n){var r=Cn(e),i=r.getQuery();if(!i)return;var s=!n.forward;return s=r.isReversed()?!s:s,Xn(e,i),Vn(e,s,i,n.repeat)},findAndSelectNextInclusive:function(e,t,n,r,i){var s=Cn(e),o=s.getQuery();if(!o)return;var u=!n.forward;u=s.isReversed()?!u:u;var a=$n(e,u,o,n.repeat,r);if(!a)return;if(i.operator)return a;var f=a[0],l=new w(a[1].line,a[1].ch-1);if(r.visualMode){if(r.visualLine||r.visualBlock)r.visualLine=!1,r.visualBlock=!1,m.signal(e,"vim-mode-change",{mode:"visual",subMode:""});var c=r.sel.anchor;if(c)return s.isReversed()?n.forward?[c,f]:[c,l]:n.forward?[c,l]:[c,f]}else r.visualMode=!0,r.visualLine=!1,r.visualBlock=!1,m.signal(e,"vim-mode-change",{mode:"visual",subMode:""});return u?[l,f]:[f,l]},goToMark:function(e,t,n,r){var i=Gn(e,r,n.selectedCharacter);return i?n.linewise?{line:i.line,ch:sn(e.getLine(i.line))}:i:null},moveToOtherHighlightedEnd:function(e,t,n,r){if(r.visualBlock&&n.sameLine){var i=r.sel;return[At(e,new w(i.anchor.line,i.head.ch)),At(e,new w(i.head.line,i.anchor.ch))]}return[r.sel.head,r.sel.anchor]},jumpToMark:function(e,t,n,r){var i=t;for(var s=0;s<n.repeat;s++){var o=i;for(var u in r.marks){if(!F(u))continue;var a=r.marks[u].find(),f=n.forward?Ft(a,o):Ft(o,a);if(f)continue;if(n.linewise&&a.line==o.line)continue;var l=jt(o,i),c=n.forward?Rt(o,a,i):Rt(i,a,o);if(l||c)i=a}}return n.linewise&&(i=new w(i.line,sn(e.getLine(i.line)))),i},moveByCharacters:function(e,t,n){var r=t,i=n.repeat,s=n.forward?r.ch+i:r.ch-i;return new w(r.line,s)},moveByLines:function(e,t,n,r){var i=t,s=i.ch;switch(r.lastMotion){case this.moveByLines:case this.moveByDisplayLines:case this.moveByScroll:case this.moveToColumn:case this.moveToEol:s=r.lastHPos;break;default:r.lastHPos=s}var o=n.repeat+(n.repeatOffset||0),u=n.forward?i.line+o:i.line-o,a=e.firstLine(),f=e.lastLine();if(u<a&&i.line==a)return this.moveToStartOfLine(e,t,n,r);if(u>f&&i.line==f)return vn(e,t,n,r,!0);var l=e.ace.session.getFoldLine(u);return l&&(n.forward?u>l.start.row&&(u=l.end.row+1):u=l.start.row),n.toFirstChar&&(s=sn(e.getLine(u)),r.lastHPos=s),r.lastHSPos=e.charCoords(new w(u,s),"div").left,new w(u,s)},moveByDisplayLines:function(e,t,n,r){var i=t;switch(r.lastMotion){case this.moveByDisplayLines:case this.moveByScroll:case this.moveByLines:case this.moveToColumn:case this.moveToEol:break;default:r.lastHSPos=e.charCoords(i,"div").left}var s=n.repeat,o=e.findPosV(i,n.forward?s:-s,"line",r.lastHSPos);if(o.hitSide)if(n.forward)var u=e.charCoords(o,"div"),a={top:u.top+8,left:r.lastHSPos},o=e.coordsChar(a,"div");else{var f=e.charCoords(new w(e.firstLine(),0),"div");f.left=r.lastHSPos,o=e.coordsChar(f,"div")}return r.lastHPos=o.ch,o},moveByPage:function(e,t,n){var r=t,i=n.repeat;return e.findPosV(r,n.forward?i:-i,"page")},moveByParagraph:function(e,t,n){var r=n.forward?1:-1;return wn(e,t,n.repeat,r)},moveBySentence:function(e,t,n){var r=n.forward?1:-1;return Sn(e,t,n.repeat,r)},moveByScroll:function(e,t,n,r){var i=e.getScrollInfo(),s=null,o=n.repeat;o||(o=i.clientHeight/(2*e.defaultTextHeight()));var u=e.charCoords(t,"local");n.repeat=o,s=St.moveByDisplayLines(e,t,n,r);if(!s)return null;var a=e.charCoords(s,"local");return e.scrollTo(null,i.top+a.top-u.top),s},moveByWords:function(e,t,n){return dn(e,t,n.repeat,!!n.forward,!!n.wordEnd,!!n.bigWord)},moveTillCharacter:function(e,t,n){var r=n.repeat,i=mn(e,r,n.forward,n.selectedCharacter,t),s=n.forward?-1:1;return fn(s,n),i?(i.ch+=s,i):null},moveToCharacter:function(e,t,n){var r=n.repeat;return fn(0,n),mn(e,r,n.forward,n.selectedCharacter,t)||t},moveToSymbol:function(e,t,n){var r=n.repeat;return hn(e,r,n.forward,n.selectedCharacter)||t},moveToColumn:function(e,t,n,r){var i=n.repeat;return r.lastHPos=i-1,r.lastHSPos=e.charCoords(t,"div").left,gn(e,i)},moveToEol:function(e,t,n,r){return vn(e,t,n,r,!1)},moveToFirstNonWhiteSpaceCharacter:function(e,t){var n=t;return new w(n.line,sn(e.getLine(n.line)))},moveToMatchedSymbol:function(e,t){var n=t,r=n.line,i=n.ch,s=e.getLine(r),o;for(;i<s.length;i++){o=s.charAt(i);if(o&&I(o)){var u=e.getTokenTypeAt(new w(r,i+1));if(u!=="string"&&u!=="comment")break}}if(i<s.length){var a=/[<>]/.test(s[i])?/[(){}[\]<>]/:/[(){}[\]]/,f=e.findMatchingBracket(new w(r,i+1),{bracketRegex:a});return f.to}return n},moveToStartOfLine:function(e,t){return new w(t.line,0)},moveToLineOrEdgeOfDocument:function(e,t,n){var r=n.forward?e.lastLine():e.firstLine();return n.repeatIsExplicit&&(r=n.repeat-e.getOption("firstLineNumber")),new w(r,sn(e.getLine(r)))},moveToStartOfDisplayLine:function(e){return e.execCommand("goLineLeft"),e.getCursor()},moveToEndOfDisplayLine:function(e){e.execCommand("goLineRight");var t=e.getCursor();return t.sticky=="before"&&t.ch--,t},textObjectManipulation:function(e,t,n,r){var i={"(":")",")":"(","{":"}","}":"{","[":"]","]":"[","<":">",">":"<"},s={"'":!0,'"':!0,"`":!0},o=n.selectedCharacter;o=="b"?o="(":o=="B"&&(o="{");var u=!n.textObjectInner,a,f;if(i[o]){f=!0,a=xn(e,t,o,u);if(!a){var l=e.getSearchCursor(new RegExp("\\"+o,"g"),t);l.find()&&(a=xn(e,l.from(),o,u))}}else if(s[o])f=!0,a=Tn(e,t,o,u);else if(o==="W"||o==="w"){var c=n.repeat||1;while(c-->0){var h=on(e,{inclusive:u,innerWord:!u,bigWord:o==="W",noSymbol:o==="W",multiline:!0},a&&a.end);h&&(a||(a=h),a.end=h.end)}}else if(o==="p"){a=wn(e,t,n.repeat,0,u),n.linewise=!0;if(r.visualMode)r.visualLine||(r.visualLine=!0);else{var p=r.inputState.operatorArgs;p&&(p.linewise=!0),a.end.line--}}else if(o==="t")a=un(e,t,u);else if(o==="s"){var d=e.getLine(t.line);t.ch>0&&z(d[t.ch])&&(t.ch-=1);var v=En(e,t,n.repeat,1,u),m=En(e,t,n.repeat,-1,u);U(e.getLine(m.line)[m.ch])&&U(e.getLine(v.line)[v.ch-1])&&(m={line:m.line,ch:m.ch+1}),a={start:m,end:v}}return a?e.state.vim.visualMode?Gt(e,a.start,a.end,f):[a.start,a.end]:null},repeatLastCharacterSearch:function(e,t,n){var r=Z.lastCharacterSearch,i=n.repeat,s=n.forward===r.forward,o=(r.increment?1:0)*(s?-1:1);e.moveH(-o,"char"),n.inclusive=s?!0:!1;var u=mn(e,i,s,r.selectedCharacter);return u?(u.ch+=o,u):(e.moveH(o,"char"),t)}},Nt={change:function(e,t,n){var r,i,s=e.state.vim,o=n[0].anchor,u=n[0].head;if(!s.visualMode){i=e.getRange(o,u);var a=s.lastEditInputState||{};if(a.motion=="moveByWords"&&!U(i)){var f=/\s+$/.exec(i);f&&a.motionArgs&&a.motionArgs.forward&&(u=Mt(u,0,-f[0].length),i=i.slice(0,-f[0].length))}t.linewise&&(o=new w(o.line,sn(e.getLine(o.line))),u.line>o.line&&(u=new w(u.line-1,Number.MAX_VALUE))),e.replaceRange("",o,u),r=o}else if(t.fullLine)u.ch=Number.MAX_VALUE,u.line--,e.setSelection(o,u),i=e.getSelection(),e.replaceSelection(""),r=o;else{i=e.getSelection();var l=Tt("",n.length);e.replaceSelections(l),r=It(n[0].head,n[0].anchor)}Z.registerController.pushText(t.registerName,"change",i,t.linewise,n.length>1),kt.enterInsertMode(e,{head:r},e.state.vim)},"delete":function(e,t,n){var r,i,s=e.state.vim;if(!s.visualBlock){var o=n[0].anchor,u=n[0].head;t.linewise&&u.line!=e.firstLine()&&o.line==e.lastLine()&&o.line==u.line-1&&(o.line==e.firstLine()?o.ch=0:o=new w(o.line-1,Ut(e,o.line-1))),i=e.getRange(o,u),e.replaceRange("",o,u),r=o,t.linewise&&(r=St.moveToFirstNonWhiteSpaceCharacter(e,o))}else{i=e.getSelection();var a=Tt("",n.length);e.replaceSelections(a),r=It(n[0].head,n[0].anchor)}return Z.registerController.pushText(t.registerName,"delete",i,t.linewise,s.visualBlock),At(e,r)},indent:function(e,t,n){var r=e.state.vim,i=r.visualMode?t.repeat:1;if(r.visualBlock){var s=e.getOption("tabSize"),o=e.getOption("indentWithTabs")?"	":" ".repeat(s),u;for(var a=n.length-1;a>=0;a--){u=It(n[a].anchor,n[a].head);if(t.indentRight)e.replaceRange(o.repeat(i),u,u);else{var f=e.getLine(u.line),l=0;for(var c=0;c<i;c++){var h=f[u.ch+l];if(h=="	")l++;else{if(h!=" ")break;l++;for(var p=1;p<o.length;p++){h=f[u.ch+l];if(h!==" ")break;l++}}}e.replaceRange("",u,Mt(u,0,l))}}return u}if(e.indentMore)for(var c=0;c<i;c++)t.indentRight?e.indentMore():e.indentLess();else{var d=n[0].anchor.line,v=r.visualBlock?n[n.length-1].anchor.line:n[0].head.line;t.linewise&&v--;for(var a=d;a<=v;a++)for(var c=0;c<i;c++)e.indentLine(a,t.indentRight)}return St.moveToFirstNonWhiteSpaceCharacter(e,n[0].anchor)},indentAuto:function(e,t,n){return e.execCommand("indentAuto"),St.moveToFirstNonWhiteSpaceCharacter(e,n[0].anchor)},hardWrap:function(e,t,n,r,i){if(!e.hardWrap)return;var s=n[0].anchor.line,o=n[0].head.line;t.linewise&&o--;var u=e.hardWrap({from:s,to:o});return u>s&&t.linewise&&u--,t.keepCursor?r:new w(u,0)},changeCase:function(e,t,n,r,i){var s=e.getSelections(),o=[],u=t.toLower;for(var a=0;a<s.length;a++){var f=s[a],l="";if(u===!0)l=f.toLowerCase();else if(u===!1)l=f.toUpperCase();else for(var c=0;c<f.length;c++){var h=f.charAt(c);l+=R(h)?h.toLowerCase():h.toUpperCase()}o.push(l)}return e.replaceSelections(o),t.shouldMoveCursor?i:!e.state.vim.visualMode&&t.linewise&&n[0].anchor.line+1==n[0].head.line?St.moveToFirstNonWhiteSpaceCharacter(e,r):t.linewise?r:It(n[0].anchor,n[0].head)},yank:function(e,t,n,r){var i=e.state.vim,s=e.getSelection(),o=i.visualMode?It(i.sel.anchor,i.sel.head,n[0].head,n[0].anchor):r;return Z.registerController.pushText(t.registerName,"yank",s,t.linewise,i.visualBlock),o}},kt={jumpListWalk:function(e,t,n){if(n.visualMode)return;var r=t.repeat,i=t.forward,s=Z.jumpList,o=s.move(e,i?r:-r),u=o?o.find():undefined;u=u?u:e.getCursor(),e.setCursor(u),e.ace.curOp.command.scrollIntoView="center-animate"},scroll:function(e,t,n){if(n.visualMode)return;var r=t.repeat||1,i=e.defaultTextHeight(),s=e.getScrollInfo().top,o=i*r,u=t.forward?s+o:s-o,a=Bt(e.getCursor()),f=e.charCoords(a,"local");if(t.forward)u>f.top?(a.line+=(u-f.top)/i,a.line=Math.ceil(a.line),e.setCursor(a),f=e.charCoords(a,"local"),e.scrollTo(null,f.top)):e.scrollTo(null,u);else{var l=u+e.getScrollInfo().clientHeight;l<f.bottom?(a.line-=(f.bottom-l)/i,a.line=Math.floor(a.line),e.setCursor(a),f=e.charCoords(a,"local"),e.scrollTo(null,f.bottom-e.getScrollInfo().clientHeight)):e.scrollTo(null,u)}},scrollToCursor:function(e,t){var n=e.getCursor().line,r=e.charCoords(new w(n,0),"local"),i=e.getScrollInfo().clientHeight,s=r.top;switch(t.position){case"center":s=r.bottom-i/2;break;case"bottom":var o=new w(n,e.getLine(n).length-1),u=e.charCoords(o,"local"),a=u.bottom-s;s=s-i+a}e.scrollTo(null,s)},replayMacro:function(e,t,n){var r=t.selectedCharacter,i=t.repeat,s=Z.macroModeState;r=="@"?r=s.latestRegister:s.latestRegister=r;while(i--)or(e,n,s,r)},enterMacroRecordMode:function(e,t){var n=Z.macroModeState,r=t.selectedCharacter;Z.registerController.isValidRegister(r)&&n.enterMacroRecordMode(e,r)},toggleOverwrite:function(e){e.state.overwrite?(e.toggleOverwrite(!1),e.setOption("keyMap","vim-insert"),m.signal(e,"vim-mode-change",{mode:"insert"})):(e.toggleOverwrite(!0),e.setOption("keyMap","vim-replace"),m.signal(e,"vim-mode-change",{mode:"replace"}))},enterInsertMode:function(e,t,n){if(e.getOption("readOnly"))return;n.insertMode=!0,n.insertModeRepeat=t&&t.repeat||1;var r=t?t.insertAt:null,i=n.sel,s=t.head||e.getCursor("head"),o=e.listSelections().length;if(r=="eol")s=new w(s.line,Ut(e,s.line));else if(r=="bol")s=new w(s.line,0);else if(r=="charAfter"){var u=E(e,s,Mt(s,0,1));s=u.end}else if(r=="firstNonBlank"){var u=E(e,s,St.moveToFirstNonWhiteSpaceCharacter(e,s));s=u.end}else if(r=="startOfSelectedArea"){if(!n.visualMode)return;n.visualBlock?(s=new w(Math.min(i.head.line,i.anchor.line),Math.min(i.head.ch,i.anchor.ch)),o=Math.abs(i.head.line-i.anchor.line)+1):i.head.line<i.anchor.line?s=i.head:s=new w(i.anchor.line,0)}else if(r=="endOfSelectedArea"){if(!n.visualMode)return;n.visualBlock?(s=new w(Math.min(i.head.line,i.anchor.line),Math.max(i.head.ch,i.anchor.ch)+1),o=Math.abs(i.head.line-i.anchor.line)+1):i.head.line>=i.anchor.line?s=Mt(i.head,0,1):s=new w(i.anchor.line,0)}else if(r=="inplace"){if(n.visualMode)return}else r=="lastEdit"&&(s=Yn(e)||s);e.setOption("disableInput",!1),t&&t.replace?(e.toggleOverwrite(!0),e.setOption("keyMap","vim-replace"),m.signal(e,"vim-mode-change",{mode:"replace"})):(e.toggleOverwrite(!1),e.setOption("keyMap","vim-insert"),m.signal(e,"vim-mode-change",{mode:"insert"})),Z.macroModeState.isPlaying||(e.on("change",lr),n.insertEnd&&n.insertEnd.clear(),n.insertEnd=e.setBookmark(s,{insertLeft:!0}),m.on(e.getInputField(),"keydown",dr)),n.visualMode&&tn(e),$t(e,s,o)},toggleVisualMode:function(e,t,n){var r=t.repeat,i=e.getCursor(),s;if(!n.visualMode){n.visualMode=!0,n.visualLine=!!t.linewise,n.visualBlock=!!t.blockwise,s=At(e,new w(i.line,i.ch+r-1));var o=E(e,i,s);n.sel={anchor:o.start,head:o.end},m.signal(e,"vim-mode-change",{mode:"visual",subMode:n.visualLine?"linewise":n.visualBlock?"blockwise":""}),Yt(e),yn(e,n,"<",It(i,s)),yn(e,n,">",qt(i,s))}else n.visualLine^t.linewise||n.visualBlock^t.blockwise?(n.visualLine=!!t.linewise,n.visualBlock=!!t.blockwise,m.signal(e,"vim-mode-change",{mode:"visual",subMode:n.visualLine?"linewise":n.visualBlock?"blockwise":""}),Yt(e)):tn(e)},reselectLastSelection:function(e,t,n){var r=n.lastSelection;n.visualMode&&Qt(e,n);if(r){var i=r.anchorMark.find(),s=r.headMark.find();if(!i||!s)return;n.sel={anchor:i,head:s},n.visualMode=!0,n.visualLine=r.visualLine,n.visualBlock=r.visualBlock,Yt(e),yn(e,n,"<",It(i,s)),yn(e,n,">",qt(i,s)),m.signal(e,"vim-mode-change",{mode:"visual",subMode:n.visualLine?"linewise":n.visualBlock?"blockwise":""})}},joinLines:function(e,t,n){var r,i;if(n.visualMode){r=e.getCursor("anchor"),i=e.getCursor("head");if(Ft(i,r)){var s=i;i=r,r=s}i.ch=Ut(e,i.line)-1}else{var o=Math.max(t.repeat,2);r=e.getCursor(),i=At(e,new w(r.line+o-1,Infinity))}var u=0;for(var a=r.line;a<i.line;a++){u=Ut(e,r.line);var f="",l=0;if(!t.keepSpaces){var c=e.getLine(r.line+1);l=c.search(/\S/),l==-1?l=c.length:f=" "}e.replaceRange(f,new w(r.line,u),new w(r.line+1,l))}var h=At(e,new w(r.line,u));n.visualMode&&tn(e,!1),e.setCursor(h)},newLineAndEnterInsertMode:function(e,t,n){n.insertMode=!0;var r=Bt(e.getCursor());if(r.line===e.firstLine()&&!t.after)e.replaceRange("\n",new w(e.firstLine(),0)),e.setCursor(e.firstLine(),0);else{r.line=t.after?r.line:r.line-1,r.ch=Ut(e,r.line),e.setCursor(r);var i=m.commands.newlineAndIndentContinueComment||m.commands.newlineAndIndent;i(e)}this.enterInsertMode(e,{repeat:t.repeat},n)},paste:function(e,t,n){var r=this,i=Z.registerController.getRegister(t.registerName),s=function(){var s=i.toString();r.continuePaste(e,t,n,s,i)};t.registerName==="+"&&typeof navigator!="undefined"&&typeof navigator.clipboard!="undefined"&&typeof navigator.clipboard.readText=="function"?navigator.clipboard.readText().then(function(s){r.continuePaste(e,t,n,s,i)},function(){s()}):s()},continuePaste:function(e,t,n,r,i){var s=Bt(e.getCursor());if(!r)return;if(t.matchIndent){var o=e.getOption("tabSize"),u=function(e){var t=e.split("	").length-1,n=e.split(" ").length-1;return t*o+n*1},a=e.getLine(e.getCursor().line),f=u(a.match(/^\s*/)[0]),l=r.replace(/\n$/,""),c=r!==l,h=u(r.match(/^\s*/)[0]),r=l.replace(/^\s*/gm,function(t){var n=f+(u(t)-h);if(n<0)return"";if(e.getOption("indentWithTabs")){var r=Math.floor(n/o);return Array(r+1).join("	")}return Array(n+1).join(" ")});r+=c?"\n":""}if(t.repeat>1)var r=Array(t.repeat+1).join(r);var p=i.linewise,d=i.blockwise;if(d){r=r.split("\n"),p&&r.pop();for(var v=0;v<r.length;v++)r[v]=r[v]==""?" ":r[v];s.ch+=t.after?1:0,s.ch=Math.min(Ut(e,s.line),s.ch)}else p?n.visualMode?r=n.visualLine?r.slice(0,-1):"\n"+r.slice(0,r.length-1)+"\n":t.after?(r="\n"+r.slice(0,r.length-1),s.ch=Ut(e,s.line)):s.ch=0:s.ch+=t.after?1:0;var m;if(n.visualMode){n.lastPastedText=r;var g,y=Kt(e,n),b=y[0],E=y[1],S=e.getSelection(),x=e.listSelections(),T=(new Array(x.length)).join("1").split("1");n.lastSelection&&(g=n.lastSelection.headMark.find()),Z.registerController.unnamedRegister.setText(S),d?(e.replaceSelections(T),E=new w(b.line+r.length-1,b.ch),e.setCursor(b),Vt(e,E),e.replaceSelections(r),m=b):n.visualBlock?(e.replaceSelections(T),e.setCursor(b),e.replaceRange(r,b,b),m=b):(e.replaceRange(r,b,E),m=e.posFromIndex(e.indexFromPos(b)+r.length-1)),g&&(n.lastSelection.headMark=e.setBookmark(g)),p&&(m.ch=0)}else if(d){e.setCursor(s);for(var v=0;v<r.length;v++){var N=s.line+v;N>e.lastLine()&&e.replaceRange("\n",new w(N,0));var C=Ut(e,N);C<s.ch&&Xt(e,N,s.ch)}e.setCursor(s),Vt(e,new w(s.line+r.length-1,s.ch)),e.replaceSelections(r),m=s}else{e.replaceRange(r,s);if(p){var N=t.after?s.line+1:s.line;m=new w(N,sn(e.getLine(N)))}else m=Bt(s),/\n/.test(r)||(m.ch+=r.length-(t.after?1:0))}n.visualMode&&tn(e,!1),e.setCursor(m)},undo:function(e,t){e.operation(function(){Ht(e,m.commands.undo,t.repeat)(),e.setCursor(At(e,e.getCursor("start")))})},redo:function(e,t){Ht(e,m.commands.redo,t.repeat)()},setRegister:function(e,t,n){n.inputState.registerName=t.selectedCharacter},insertRegister:function(e,t,n){var r=t.selectedCharacter,i=Z.registerController.getRegister(r),s=i&&i.toString();s&&e.replaceSelection(s)},oneNormalCommand:function(e,t,n){rr(e,!0),n.insertModeReturn=!0,m.on(e,"vim-command-done",function r(){if(n.visualMode)return;n.insertModeReturn&&(n.insertModeReturn=!1,n.insertMode||kt.enterInsertMode(e,{},n)),m.off(e,"vim-command-done",r)})},setMark:function(e,t,n){var r=t.selectedCharacter;yn(e,n,r,e.getCursor())},replace:function(e,t,n){var r=t.selectedCharacter,i=e.getCursor(),s,o,u=e.listSelections();if(n.visualMode)i=e.getCursor("start"),o=e.getCursor("end");else{var a=e.getLine(i.line);s=i.ch+t.repeat,s>a.length&&(s=a.length),o=new w(i.line,s)}var f=E(e,i,o);i=f.start,o=f.end;if(r=="\n")n.visualMode||e.replaceRange("",i,o),(m.commands.newlineAndIndentContinueComment||m.commands.newlineAndIndent)(e);else{var l=e.getRange(i,o);l=l.replace(/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,r),l=l.replace(/[^\n]/g,r);if(n.visualBlock){var c=(new Array(e.getOption("tabSize")+1)).join(" ");l=e.getSelection(),l=l.replace(/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,r),l=l.replace(/\t/g,c).replace(/[^\n]/g,r).split("\n"),e.replaceSelections(l)}else e.replaceRange(l,i,o);n.visualMode?(i=Ft(u[0].anchor,u[0].head)?u[0].anchor:u[0].head,e.setCursor(i),tn(e,!1)):e.setCursor(Mt(o,0,-1))}},incrementNumberToken:function(e,t){var n=e.getCursor(),r=e.getLine(n.line),i=/(-?)(?:(0x)([\da-f]+)|(0b|0|)(\d+))/gi,s,o,u,a;while((s=i.exec(r))!==null){o=s.index,u=o+s[0].length;if(n.ch<u)break}if(!t.backtrack&&u<=n.ch)return;if(!s)return;var f=s[2]||s[4],l=s[3]||s[5],c=t.increase?1:-1,h={"0b":2,0:8,"":10,"0x":16}[f.toLowerCase()],p=parseInt(s[1]+l,h)+c*t.repeat;a=p.toString(h);var d=f?(new Array(l.length-a.length+1+s[1].length)).join("0"):"";a.charAt(0)==="-"?a="-"+f+d+a.substr(1):a=f+d+a;var v=new w(n.line,o),m=new w(n.line,u);e.replaceRange(a,v,m),e.setCursor(new w(n.line,o+a.length-1))},repeatLastEdit:function(e,t,n){var r=n.lastEditInputState;if(!r)return;var i=t.repeat;i&&t.repeatIsExplicit?n.lastEditInputState.repeatOverride=i:i=n.lastEditInputState.repeatOverride||i,vr(e,n,i,!1)},indent:function(e,t){e.indentLine(e.getCursor().line,t.indentRight)},exitInsertMode:rr},ln={"(":"bracket",")":"bracket","{":"bracket","}":"bracket","[":"section","]":"section","*":"comment","/":"comment",m:"method",M:"method","#":"preprocess"},cn={bracket:{isComplete:function(e){if(e.nextCh===e.symb){e.depth++;if(e.depth>=1)return!0}else e.nextCh===e.reverseSymb&&e.depth--;return!1}},section:{init:function(e){e.curMoveThrough=!0,e.symb=(e.forward?"]":"[")===e.symb?"{":"}"},isComplete:function(e){return e.index===0&&e.nextCh===e.symb}},comment:{isComplete:function(e){var t=e.lastCh==="*"&&e.nextCh==="/";return e.lastCh=e.nextCh,t}},method:{init:function(e){e.symb=e.symb==="m"?"{":"}",e.reverseSymb=e.symb==="{"?"}":"{"},isComplete:function(e){return e.nextCh===e.symb?!0:!1}},preprocess:{init:function(e){e.index=0},isComplete:function(e){if(e.nextCh==="#"){var t=e.lineText.match(/^#(\w+)/)[1];if(t==="endif"){if(e.forward&&e.depth===0)return!0;e.depth++}else if(t==="if"){if(!e.forward&&e.depth===0)return!0;e.depth--}if(t==="else"&&e.depth===0)return!0}return!1}}};V("pcre",!0,"boolean"),Nn.prototype={getQuery:function(){return Z.query},setQuery:function(e){Z.query=e},getOverlay:function(){return this.searchOverlay},setOverlay:function(e){this.searchOverlay=e},isReversed:function(){return Z.isReversed},setReversed:function(e){Z.isReversed=e},getScrollbarAnnotate:function(){return this.annotate},setScrollbarAnnotate:function(e){this.annotate=e}};var _n={"\\n":"\n","\\r":"\r","\\t":"	"},Pn={"\\/":"/","\\\\":"\\","\\n":"\n","\\r":"\r","\\t":"	","\\&":"&"},Wn=0,Zn=function(){this.buildCommandMap_()};Zn.prototype={processCommand:function(e,t,n){var r=this;e.operation(function(){e.curOp.isVimOp=!0,r._processCommand(e,t,n)})},_processCommand:function(e,t,n){var r=e.state.vim,i=Z.registerController.getRegister(":"),s=i.toString(),o=new m.StringStream(t);i.setText(t);var u=n||{};u.input=t;try{this.parseInput_(e,o,u)}catch(a){throw Fn(e,a.toString()),a}r.visualMode&&tn(e);var f,l;if(!u.commandName)u.line!==undefined&&(l="move");else{f=this.matchCommand_(u.commandName);if(f){l=f.name,f.excludeFromCommandHistory&&i.setText(s),this.parseCommandArgs_(o,u,f);if(f.type=="exToKey"){ut(e,f.toKeys,f);return}if(f.type=="exToEx"){this.processCommand(e,f.toInput);return}}}if(!l){Fn(e,'Not an editor command ":'+t+'"');return}try{er[l](e,u),(!f||!f.possiblyAsync)&&u.callback&&u.callback()}catch(a){throw Fn(e,a.toString()),a}},parseInput_:function(e,t,n){t.eatWhile(":"),t.eat("%")?(n.line=e.firstLine(),n.lineEnd=e.lastLine()):(n.line=this.parseLineSpec_(e,t),n.line!==undefined&&t.eat(",")&&(n.lineEnd=this.parseLineSpec_(e,t)));if(n.line==undefined)if(e.state.vim.visualMode){var r=Gn(e,e.state.vim,"<");n.selectionLine=r&&r.line,r=Gn(e,e.state.vim,">"),n.selectionLineEnd=r&&r.line}else n.selectionLine=e.getCursor().line;else n.selectionLine=n.line,n.selectionLineEnd=n.lineEnd;var i=t.match(/^(\w+|!!|@@|[!#&*<=>@~])/);return i?n.commandName=i[1]:n.commandName=t.match(/.*/)[0],n},parseLineSpec_:function(e,t){var n=t.match(/^(\d+)/);if(n)return parseInt(n[1],10)-1;switch(t.next()){case".":return this.parseLineSpecOffset_(t,e.getCursor().line);case"$":return this.parseLineSpecOffset_(t,e.lastLine());case"'":var r=t.next(),i=Gn(e,e.state.vim,r);if(!i)throw new Error("Mark not set");return this.parseLineSpecOffset_(t,i.line);case"-":case"+":return t.backUp(1),this.parseLineSpecOffset_(t,e.getCursor().line);default:return t.backUp(1),undefined}},parseLineSpecOffset_:function(e,t){var n=e.match(/^([+-])?(\d+)/);if(n){var r=parseInt(n[2],10);n[1]=="-"?t-=r:t+=r}return t},parseCommandArgs_:function(e,t,n){if(e.eol())return;t.argString=e.match(/.*/)[0];var r=n.argDelimiter||/\s+/,i=zt(t.argString).split(r);i.length&&i[0]&&(t.args=i)},matchCommand_:function(e){for(var t=e.length;t>0;t--){var n=e.substring(0,t);if(this.commandMap_[n]){var r=this.commandMap_[n];if(r.name.indexOf(e)===0)return r}}return null},buildCommandMap_:function(){this.commandMap_={};for(var e=0;e<T.length;e++){var t=T[e],n=t.shortName||t.name;this.commandMap_[n]=t}},map:function(e,t,n,r){if(e!=":"&&e.charAt(0)==":"){if(n)throw Error("Mode not supported for ex mappings");var i=e.substring(1);t!=":"&&t.charAt(0)==":"?this.commandMap_[i]={name:i,type:"exToEx",toInput:t.substring(1),user:!0}:this.commandMap_[i]={name:i,type:"exToKey",toKeys:t,user:!0}}else{var s={keys:e,type:"keyToKey",toKeys:t,noremap:!!r};n&&(s.context=n),S.unshift(s)}},unmap:function(e,t){if(e!=":"&&e.charAt(0)==":"){if(t)throw Error("Mode not supported for ex mappings");var n=e.substring(1);if(this.commandMap_[n]&&this.commandMap_[n].user)return delete this.commandMap_[n],!0}else{var r=e;for(var i=0;i<S.length;i++)if(r==S[i].keys&&S[i].context===t)return S.splice(i,1),!0}}};var er={colorscheme:function(e,t){if(!t.args||t.args.length<1){Fn(e,e.getOption("theme"));return}e.setOption("theme",t.args[0])},map:function(e,t,n,r){var i=t.args;if(!i||i.length<2){e&&Fn(e,"Invalid mapping: "+t.input);return}tr.map(i[0],i[1],n,r)},imap:function(e,t){this.map(e,t,"insert")},nmap:function(e,t){this.map(e,t,"normal")},vmap:function(e,t){this.map(e,t,"visual")},omap:function(e,t){this.map(e,t,"operatorPending")},noremap:function(e,t){this.map(e,t,undefined,!0)},inoremap:function(e,t){this.map(e,t,"insert",!0)},nnoremap:function(e,t){this.map(e,t,"normal",!0)},vnoremap:function(e,t){this.map(e,t,"visual",!0)},onoremap:function(e,t){this.map(e,t,"operatorPending",!0)},unmap:function(e,t,n){var r=t.args;(!r||r.length<1||!tr.unmap(r[0],n))&&e&&Fn(e,"No such mapping: "+t.input)},mapclear:function(e,t){nt.mapclear()},imapclear:function(e,t){nt.mapclear("insert")},nmapclear:function(e,t){nt.mapclear("normal")},vmapclear:function(e,t){nt.mapclear("visual")},omapclear:function(e,t){nt.mapclear("operatorPending")},move:function(e,t){Et.processCommand(e,e.state.vim,{type:"motion",motion:"moveToLineOrEdgeOfDocument",motionArgs:{forward:!1,explicitRepeat:!0,linewise:!0},repeatOverride:t.line+1})},set:function(e,t){var n=t.args,r=t.setCfg||{};if(!n||n.length<1){e&&Fn(e,"Invalid mapping: "+t.input);return}var i=n[0].split("="),s=i[0],o=i[1],u=!1,a=!1;if(s.charAt(s.length-1)=="?"){if(o)throw Error("Trailing characters: "+t.argString);s=s.substring(0,s.length-1),u=!0}else s.charAt(s.length-1)=="!"&&(s=s.substring(0,s.length-1),a=!0);o===undefined&&s.substring(0,2)=="no"&&(s=s.substring(2),o=!1);var f=X[s]&&X[s].type=="boolean";f&&(a?o=!J(s,e,r):o==undefined&&(o=!0));if(!f&&o===undefined||u){var l=J(s,e,r);l instanceof Error?Fn(e,l.message):l===!0||l===!1?Fn(e," "+(l?"":"no")+s):Fn(e,"  "+s+"="+l)}else{var c=$(s,o,e,r);c instanceof Error&&Fn(e,c.message)}},setlocal:function(e,t){t.setCfg={scope:"local"},this.set(e,t)},setglobal:function(e,t){t.setCfg={scope:"global"},this.set(e,t)},registers:function(e,t){var n=t.args,r=Z.registerController.registers,i="----------Registers----------\n\n";if(!n)for(var s in r){var o=r[s].toString();o.length&&(i+='"'+s+"    "+o+"\n")}else{var s;n=n.join("");for(var u=0;u<n.length;u++){s=n.charAt(u);if(!Z.registerController.isValidRegister(s))continue;var a=r[s]||new gt;i+='"'+s+"    "+a.toString()+"\n"}}Fn(e,i)},sort:function(e,t){function u(){if(t.argString){var e=new m.StringStream(t.argString);e.eat("!")&&(n=!0);if(e.eol())return;if(!e.eatSpace())return"Invalid arguments";var u=e.match(/([dinuox]+)?\s*(\/.+\/)?\s*/);if(!u&&!e.eol())return"Invalid arguments";if(u[1]){r=u[1].indexOf("i")!=-1,i=u[1].indexOf("u")!=-1;var a=u[1].indexOf("d")!=-1||u[1].indexOf("n")!=-1&&1,f=u[1].indexOf("x")!=-1&&1,l=u[1].indexOf("o")!=-1&&1;if(a+f+l>1)return"Invalid arguments";s=a&&"decimal"||f&&"hex"||l&&"octal"}u[2]&&(o=new RegExp(u[2].substr(1,u[2].length-2),r?"i":""))}}function S(e,t){if(n){var i;i=e,e=t,t=i}r&&(e=e.toLowerCase(),t=t.toLowerCase());var o=s&&d.exec(e),u=s&&d.exec(t);return o?(o=parseInt((o[1]+o[2]).toLowerCase(),v),u=parseInt((u[1]+u[2]).toLowerCase(),v),o-u):e<t?-1:1}function x(e,t){if(n){var i;i=e,e=t,t=i}return r&&(e[0]=e[0].toLowerCase(),t[0]=t[0].toLowerCase()),e[0]<t[0]?-1:1}var n,r,i,s,o,a=u();if(a){Fn(e,a+": "+t.argString);return}var f=t.line||e.firstLine(),l=t.lineEnd||t.line||e.lastLine();if(f==l)return;var c=new w(f,0),h=new w(l,Ut(e,l)),p=e.getRange(c,h).split("\n"),d=o?o:s=="decimal"?/(-?)([\d]+)/:s=="hex"?/(-?)(?:0x)?([0-9a-f]+)/i:s=="octal"?/([0-7]+)/:null,v=s=="decimal"?10:s=="hex"?16:s=="octal"?8:null,g=[],y=[];if(s||o)for(var b=0;b<p.length;b++){var E=o?p[b].match(o):null;E&&E[0]!=""?g.push(E):!o&&d.exec(p[b])?g.push(p[b]):y.push(p[b])}else y=p;g.sort(o?x:S);if(o)for(var b=0;b<g.length;b++)g[b]=g[b].input;else s||y.sort(S);p=n?g.concat(y):y.concat(g);if(i){var T=p,N;p=[];for(var b=0;b<T.length;b++)T[b]!=N&&p.push(T[b]),N=T[b]}e.replaceRange(p.join("\n"),c,h)},vglobal:function(e,t){this.global(e,t)},normal:function(e,t){var n=t.argString;n&&n[0]=="!"&&(n=n.slice(1),it=!0),n=n.trimStart();if(!n){Fn(e,"Argument is required.");return}var r=t.line;if(typeof r=="number"){var i=isNaN(t.lineEnd)?r:t.lineEnd;for(var s=r;s<=i;s++)e.setCursor(s,0),ut(e,t.argString.trimStart()),e.state.vim.insertMode&&rr(e,!0)}else ut(e,t.argString.trimStart()),e.state.vim.insertMode&&rr(e,!0)},global:function(e,t){var n=t.argString;if(!n){Fn(e,"Regular Expression missing from global");return}var r=t.commandName[0]==="v";n[0]==="!"&&t.commandName[0]==="g"&&(r=!0,n=n.slice(1));var i=t.line!==undefined?t.line:e.firstLine(),s=t.lineEnd||t.line||e.lastLine(),o=kn(n),u=n,a;o.length&&(u=o[0],a=o.slice(1,o.length).join("/"));if(u)try{Un(e,u,!0,!0)}catch(f){Fn(e,"Invalid regex: "+u);return}var l=Cn(e).getQuery(),c=[];for(var h=i;h<=s;h++){var p=e.getLine(h),d=l.test(p);d!==r&&c.push(a?e.getLineHandle(h):p)}if(!a){Fn(e,c.join("\n"));return}var v=0,m=function(){if(v<c.length){var t=c[v++],n=e.getLineNumber(t);if(n==null){m();return}var r=n+1+a;tr.processCommand(e,r,{callback:m})}else e.releaseLineHandles&&e.releaseLineHandles()};m()},substitute:function(e,t){if(!e.getSearchCursor)throw new Error("Search feature not available. Requires searchcursor.js or any other getSearchCursor implementation.");var n=t.argString,r=n?An(n,n[0]):[],i,s="",o,u,a,f=!1,l=!1;if(r.length)i=r[0],J("pcre")&&i!==""&&(i=(new RegExp(i)).source),s=r[1],s!==undefined&&(J("pcre")?s=Hn(s.replace(/([^\\])&/g,"$1$$&")):s=Dn(s),Z.lastSubstituteReplacePart=s),o=r[2]?r[2].split(" "):[];else if(n&&n.length){Fn(e,"Substitutions should be of the form :s/pattern/replace/");return}o&&(u=o[0],a=parseInt(o[1]),u&&(u.indexOf("c")!=-1&&(f=!0),u.indexOf("g")!=-1&&(l=!0),J("pcre")?i=i+"/"+u:i=i.replace(/\//g,"\\/")+"/"+u));if(i)try{Un(e,i,!0,!0)}catch(c){Fn(e,"Invalid regex: "+i);return}s=s||Z.lastSubstituteReplacePart;if(s===undefined){Fn(e,"No previous substitute regular expression");return}var h=Cn(e),p=h.getQuery(),d=t.line!==undefined?t.line:e.getCursor().line,v=t.lineEnd||d;d==e.firstLine()&&v==e.lastLine()&&(v=Infinity),a&&(d=v,v=d+a-1);var m=At(e,new w(d,0)),g=e.getSearchCursor(p,m);nr(e,f,l,d,v,g,p,s,t.callback)},startinsert:function(e,t){ut(e,t.argString=="!"?"A":"i",{})},redo:m.commands.redo,undo:m.commands.undo,write:function(e){m.commands.save?m.commands.save(e):e.save&&e.save()},nohlsearch:function(e){Jn(e)},yank:function(e){var t=Bt(e.getCursor()),n=t.line,r=e.getLine(n);Z.registerController.pushText("0","yank",r,!0,!0)},"delete":function(e,t){var n=t.selectionLine,r=isNaN(t.selectionLineEnd)?n:t.selectionLineEnd;Nt.delete(e,{linewise:!0},[{anchor:new w(n,0),head:new w(r+1,0)}])},join:function(e,t){var n=t.selectionLine,r=isNaN(t.selectionLineEnd)?n:t.selectionLineEnd;e.setCursor(new w(n,0)),kt.joinLines(e,{repeat:r-n},e.state.vim)},delmarks:function(e,t){if(!t.argString||!zt(t.argString)){Fn(e,"Argument required");return}var n=e.state.vim,r=new m.StringStream(zt(t.argString));while(!r.eol()){r.eatSpace();var i=r.pos;if(!r.match(/[a-zA-Z]/,!1)){Fn(e,"Invalid argument: "+t.argString.substring(i));return}var s=r.next();if(r.match("-",!0)){if(!r.match(/[a-zA-Z]/,!1)){Fn(e,"Invalid argument: "+t.argString.substring(i));return}var o=s,u=r.next();if(!(F(o)&&F(u)||R(o)&&R(u))){Fn(e,"Invalid argument: "+o+"-");return}var a=o.charCodeAt(0),f=u.charCodeAt(0);if(a>=f){Fn(e,"Invalid argument: "+t.argString.substring(i));return}for(var l=0;l<=f-a;l++){var c=String.fromCharCode(a+l);delete n.marks[c]}}else delete n.marks[s]}}},tr=new Zn;V("insertModeEscKeysTimeout",200,"number"),m.Vim=nt;var yr={"return":"CR",backspace:"BS","delete":"Del",esc:"Esc",left:"Left",right:"Right",up:"Up",down:"Down",space:"Space",insert:"Ins",home:"Home",end:"End",pageup:"PageUp",pagedown:"PageDown",enter:"CR"},wr=nt.handleKey.bind(nt);nt.handleKey=function(e,t,n){return e.operation(function(){return wr(e,t,n)},!0)},et(),t.CodeMirror=m;var xr=nt.maybeInitVimState_;t.handler={$id:"ace/keyboard/vim",drawCursor:function(e,t,n,r,s){var u=this.state.vim||{},a=n.characterWidth,f=n.lineHeight,l=t.top,c=t.left;if(!u.insertMode){var h=r.cursor?i.comparePoints(r.cursor,r.start)<=0:s.selection.isBackwards()||s.selection.isEmpty();!h&&c>a&&(c-=a)}!u.insertMode&&u.status&&(f/=2,l+=f),o.translate(e,c,l),o.setStyle(e.style,"width",a+"px"),o.setStyle(e.style,"height",f+"px")},$getDirectionForHighlight:function(e){var t=e.state.cm,n=xr(t);if(!n.insertMode)return e.session.selection.isBackwards()||e.session.selection.isEmpty()},handleKeyboard:function(e,t,n,r,i){var s=e.editor,o=s.state.cm,u=xr(o);if(r==-1)return;u.insertMode||(t==-1?(n.charCodeAt(0)>255&&e.inputKey&&(n=e.inputKey,n&&e.inputHash==4&&(n=n.toUpperCase())),e.inputChar=n):t==4||t==0?e.inputKey==n&&e.inputHash==t&&e.inputChar?(n=e.inputChar,t=-1):(e.inputChar=null,e.inputKey=n,e.inputHash=t):e.inputChar=e.inputKey=null);if(o.state.overwrite&&u.insertMode&&n=="backspace"&&t==0)return{command:"gotoleft"};if(n=="c"&&t==1&&!c.isMac&&s.getCopyText())return s.once("copy",function(){u.insertMode?s.selection.clearSelection():o.operation(function(){tn(o)})}),{command:"null",passEvent:!0};if(n=="esc"&&!u.insertMode&&!u.visualMode&&!o.ace.inMultiSelectMode){var a=Cn(o),f=a.getOverlay();f&&o.removeOverlay(f)}if(t==-1||t&1||t===0&&n.length>1){var l=u.insertMode,h=br(t,n,i||{},u);u.status==null&&(u.status="");var p=Sr(o,h,"user");u=xr(o),p&&u.status!=null?u.status+=h:u.status==null&&(u.status=""),o._signal("changeStatus");if(!p&&(t!=-1||l))return;return{command:"null",passEvent:!p}}},attach:function(e){function n(){var n=xr(t).insertMode;t.ace.renderer.setStyle("normal-mode",!n),e.textInput.setCommandMode(!n),e.renderer.$keepTextAreaAtCursor=n,e.renderer.$blockCursor=!n}e.state||(e.state={});var t=new m(e);e.state.cm=t,e.$vimModeHandler=this,C(t),xr(t).status=null,t.on("vim-command-done",function(){if(t.virtualSelectionMode())return;xr(t).status=null,t.ace._signal("changeStatus"),t.ace.session.markUndoGroup()}),t.on("changeStatus",function(){t.ace.renderer.updateCursor(),t.ace._signal("changeStatus")}),t.on("vim-mode-change",function(){if(t.virtualSelectionMode())return;n(),t._signal("changeStatus")}),n(),e.renderer.$cursorLayer.drawCursor=this.drawCursor.bind(t)},detach:function(e){var t=e.state.cm;k(t),t.destroy(),e.state.cm=null,e.$vimModeHandler=null,e.renderer.$cursorLayer.drawCursor=null,e.renderer.setStyle("normal-mode",!1),e.textInput.setCommandMode(!1),e.renderer.$keepTextAreaAtCursor=!0},getStatusText:function(e){var t=e.state.cm,n=xr(t);if(n.insertMode)return"INSERT";var r="";return n.visualMode&&(r+="VISUAL",n.visualLine&&(r+=" LINE"),n.visualBlock&&(r+=" BLOCK")),n.status&&(r+=(r?" ":"")+n.status),r}},nt.defineOption({name:"wrap",set:function(e,t){t&&t.ace.setOption("wrap",e)},type:"boolean"},!1),nt.defineEx("write","w",function(){console.log(":write is not implemented")}),S.push({keys:"zc",type:"action",action:"fold",actionArgs:{open:!1}},{keys:"zC",type:"action",action:"fold",actionArgs:{open:!1,all:!0}},{keys:"zo",type:"action",action:"fold",actionArgs:{open:!0}},{keys:"zO",type:"action",action:"fold",actionArgs:{open:!0,all:!0}},{keys:"za",type:"action",action:"fold",actionArgs:{toggle:!0}},{keys:"zA",type:"action",action:"fold",actionArgs:{toggle:!0,all:!0}},{keys:"zf",type:"action",action:"fold",actionArgs:{open:!0,all:!0}},{keys:"zd",type:"action",action:"fold",actionArgs:{open:!0,all:!0}},{keys:"<C-A-k>",type:"action",action:"aceCommand",actionArgs:{name:"addCursorAbove"}},{keys:"<C-A-j>",type:"action",action:"aceCommand",actionArgs:{name:"addCursorBelow"}},{keys:"<C-A-S-k>",type:"action",action:"aceCommand",actionArgs:{name:"addCursorAboveSkipCurrent"}},{keys:"<C-A-S-j>",type:"action",action:"aceCommand",actionArgs:{name:"addCursorBelowSkipCurrent"}},{keys:"<C-A-h>",type:"action",action:"aceCommand",actionArgs:{name:"selectMoreBefore"}},{keys:"<C-A-l>",type:"action",action:"aceCommand",actionArgs:{name:"selectMoreAfter"}},{keys:"<C-A-S-h>",type:"action",action:"aceCommand",actionArgs:{name:"selectNextBefore"}},{keys:"<C-A-S-l>",type:"action",action:"aceCommand",actionArgs:{name:"selectNextAfter"}}),S.push({keys:"gq",type:"operator",operator:"hardWrap"}),nt.defineOperator("hardWrap",function(e,t,n,r,i){var s=n[0].anchor.line,o=n[0].head.line;return t.linewise&&o--,v(e.ace,{startRow:s,endRow:o}),w(o,0)}),V("textwidth",undefined,"number",["tw"],function(e,t){if(t===undefined)return;if(e===undefined){var n=t.ace.getOption("printMarginColumn");return n}var r=Math.round(e);r>1&&t.ace.setOption("printMarginColumn",r)}),kt.aceCommand=function(e,t,n){e.vimCmd=t,e.ace.inVirtualSelectionMode?e.ace.on("beforeEndOperation",Tr):Tr(null,e.ace)},kt.fold=function(e,t,n){e.ace.execCommand(["toggleFoldWidget","toggleFoldWidget","foldOther","unfoldall"][(t.all?2:0)+(t.open?1:0)])},x=S.length,t.handler.defaultKeymap=S,t.handler.actions=kt,t.Vim=nt});                (function() {
                    window.require(["ace/keyboard/vim"], function(m) {
                        if (typeof module == "object" && typeof exports == "object" && module) {
                            module.exports = m;
                        }
                    });
                })();
            