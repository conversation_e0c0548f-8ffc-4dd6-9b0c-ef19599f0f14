<html>

<head>
    <title>Example secrets file</title>

</head>

<body>
    <div x-data>
        <p>You can store passwords and secrets in standard <code>.env</code> format, one per line.<br>
Add comments using <code>#</code> to help the agent understand the purpose of each secret.<br>
See example below.</p>


        <h3>Example secrets file</h3>
        <div id="secrets-example"></div>

        <script>
            setTimeout(() => {
                const envExample = `# Sales email credentials
SALES_EMAIL_USERNAME="<EMAIL>"
SALES_EMAIL_PASSWORD="s3cret-p4$$w0rd"

# Search engine API keys
BRAVE_API_KEY="brv_xxxxxxxxxxxxxxxxxxxxx"
GOOGLE_API_KEY="AIzaSyD-xxxxxxxxxxxxxxxxxxxx"

# Email password for notifications
EMAIL_PASSWORD="another-secret-password"
`;

                const editor = ace.edit("secrets-example");
                const dark = localStorage.getItem("darkMode");
                if (dark != "false") {
                    editor.setTheme("ace/theme/github_dark");
                } else {
                    editor.setTheme("ace/theme/tomorrow");
                }
                editor.session.setMode("ace/mode/ini");
                editor.setValue(envExample);
                editor.clearSelection();
                editor.setReadOnly(true);
            }, 0);
        </script>
        <!-- </template> -->
    </div>

    <style>
        #secrets-example {
            width: 100%;
            height: 20em;
        }
    </style>

</body>

</html>