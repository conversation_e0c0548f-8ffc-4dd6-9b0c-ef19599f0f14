<html>

<head>
    <title>Connection to A0 MCP Server</title>

</head>

<body>
    <div x-data>
        <p>Agent Zero MCP Server is an SSE MCP running on the same URL and port as the Web UI + /mcp/sse path.</p>
        <p>The same applies if you run A0 on a public URL using a tunnel.</p>

        <!-- API Token Information -->
        <div style="background-color: var(--color-bg-secondary); border: 1px solid var(--color-border); border-radius: 6px; padding: 12px; margin: 16px 0;">
            <h4 style="margin: 0 0 8px 0; color: var(--color-text-primary);">API Token Information</h4>
            <p style="margin: 0; color: var(--color-text-secondary); font-size: 14px;">
                The token used in the URL is automatically generated from your username and password.
                This same token is also used for external API endpoints. The token changes when you update your credentials.
            </p>
        </div>

        <h3>Example MCP Server Configuration JSON</h3>
        <div id="mcp-server-example"></div>

        <script>
            setTimeout(() => {
                const url = window.location.origin;
                const token = settingsModalProxy.settings.sections.filter(x => x.id == "mcp_server")[0].fields.filter(x => x.id == "mcp_server_token")[0].value;
                const jsonExample = JSON.stringify({
                    "mcpServers":
                    {
                        "agent-zero": {
                            "type": "sse",
                            "url": `${url}/mcp/t-${token}/sse`
                        },
                        "agent-zero-http": {
                            "type": "streamable-http",
                            "url": `${url}/mcp/t-${token}/http/`
                        }
                    }
                }, null, 2);

                const editor = ace.edit("mcp-server-example");
                const dark = localStorage.getItem("darkMode");
                if (dark != "false") {
                    editor.setTheme("ace/theme/github_dark");
                } else {
                    editor.setTheme("ace/theme/tomorrow");
                }
                editor.session.setMode("ace/mode/json");
                editor.setValue(jsonExample);
                editor.clearSelection();
                editor.setReadOnly(true);
            }, 0);
        </script>
        <!-- </template> -->
    </div>

    <style>
        #mcp-server-example {
            width: 100%;
            height: 15em;
        }
    </style>

</body>

</html>
