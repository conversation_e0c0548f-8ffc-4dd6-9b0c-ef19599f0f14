<html>
<head>
    <script type="module">
        import { store } from "/components/notifications/notification-store.js";
    </script>
</head>
<body>
    <div x-data>
        <template x-if="$store.notificationStore">
            <!-- Notification Toggle Button -->
            <div class="notification-toggle"
                 :class="{
                     'has-unread': $store.notificationStore.unreadPrioCount > 0,
                     'has-notifications': $store.notificationStore.notifications.length > 0
                 }"
                 @click="$store.notificationStore.openModal()"
                 title="View Notifications">
                <div class="notification-icon">
                    <span class="material-symbols-outlined">notifications</span>
                </div>
                <span x-show="$store.notificationStore.unreadPrioCount > 0"
                      class="notification-badge"
                      x-text="$store.notificationStore.unreadPrioCount"></span>
            </div>
        </template>
    </div>
</body>
</html>
