from python.helpers.api import A<PERSON><PERSON><PERSON><PERSON>, Request, Response

from python.helpers import settings

class GetSettings(ApiHandler):
    async def process(self, input: dict, request: Request) -> dict | Response:
        set = settings.convert_out(settings.get_settings())
        return {"settings": set}

    @classmethod
    def get_methods(cls) -> list[str]:
        return ["GET", "POST"]
